#!/usr/bin/env python3
"""
Oprava obrázků nahrazením za největší dostupné verze
Protože originály neexistuj<PERSON>, pou<PERSON><PERSON><PERSON><PERSON> nejv<PERSON>tš<PERSON> thumbnail jako n<PERSON>
"""
import json
import re
import os
import logging
from collections import defaultdict
from db_connectors import get_mysql_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class LargestImageFinder:
    def __init__(self, image_map):
        self.image_map = image_map
        self.size_groups = defaultdict(list)
        self._group_by_base_name()
    
    def _group_by_base_name(self):
        """Seskupí obrázky podle základního názvu"""
        logging.info("Seskupuji obrázky podle základn<PERSON>ch názvů...")
        
        for path, info in self.image_map.items():
            if not isinstance(info, dict) or 'wp_url' not in info:
                continue
                
            filename = os.path.basename(info['wp_url'])
            base_name = self.get_base_filename(filename)
            article_id = self.extract_article_id(path)
            
            if not article_id:
                continue
                
            key = f"{article_id}/{base_name}"
            
            # Extrahovat rozměry
            width, height = self.extract_dimensions(filename)
            size = (width * height) if width and height else 999999  # Soubory bez rozměrů = největší
            
            self.size_groups[key].append({
                'wp_id': info['wp_id'],
                'wp_url': info['wp_url'],
                'path': path,
                'filename': filename,
                'width': width or 9999,
                'height': height or 9999,
                'size': size,
                'base_name': base_name,
                'article_id': article_id
            })
        
        # Seřadit každou skupinu podle velikosti (největší první)
        for key in self.size_groups:
            self.size_groups[key].sort(key=lambda x: x['size'], reverse=True)
        
        logging.info(f"Seskupeno {len(self.size_groups)} základních názvů")
    
    def extract_dimensions(self, filename):
        """Extrahuje rozměry z názvu souboru"""
        match = re.search(r'_(\d+)x(\d+)', filename)
        if match:
            return int(match.group(1)), int(match.group(2))
        return None, None
    
    def get_base_filename(self, filename):
        """Získá základní název souboru"""
        # Odstranit rozměry: file_800x600.jpg -> file.jpg
        base = re.sub(r'_\d+x\d+', '', filename)
        # Odstranit hash: file-abc123def.jpg -> file.jpg  
        base = re.sub(r'-[a-f0-9]{10,}', '', base)
        return base
    
    def extract_article_id(self, path):
        """Extrahuje ID článku z cesty"""
        match = re.search(r'([a-f0-9]{10,})', path)
        return match.group(1) if match else None
    
    def find_largest_version(self, current_wp_id, current_filename, current_path):
        """Najde největší verzi pro daný obrázek"""
        base_name = self.get_base_filename(current_filename)
        article_id = self.extract_article_id(current_path)
        
        if not article_id:
            return None
            
        key = f"{article_id}/{base_name}"
        
        if key in self.size_groups:
            versions = self.size_groups[key]
            
            # Najít současnou verzi
            current_version = None
            for v in versions:
                if v['wp_id'] == current_wp_id:
                    current_version = v
                    break
            
            if not current_version:
                return None
                
            # Vrátit největší verzi (první v seřazeném seznamu)
            largest = versions[0]
            
            # Pokud je největší verze jiná než současná
            if largest['wp_id'] != current_wp_id:
                return largest
        
        return None

def fix_article_with_largest_versions(article_unique_id, mysql_cursor, finder):
    """Opraví článek nahrazením za největší dostupné verze"""
    
    print(f"\n🔧 OPRAVUJI ČLÁNEK: {article_unique_id}")
    print("=" * 60)
    
    # Najít WordPress post
    mysql_cursor.execute(f"""
        SELECT p.ID, p.post_title, p.post_content, pm.meta_value as thumbnail_id
        FROM {TBL_WP_POSTS} p
        JOIN {TBL_WP_POSTMETA} pm_version ON p.ID = pm_version.post_id
        LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
        WHERE p.post_type = 'post' 
        AND pm_version.meta_key = 'sabre_database_version' 
        AND pm_version.meta_value = 'mujdum-2'
        AND p.post_content LIKE %s
    """, (f"%{article_unique_id}%",))
    
    post_result = mysql_cursor.fetchone()
    
    if not post_result:
        print("❌ WordPress post nenalezen")
        return False
        
    post_id, post_title, post_content, current_thumbnail = post_result
    print(f"📖 Post: {post_id} - {post_title}")
    
    # Najít všechny image ID v obsahu
    image_ids = set()
    
    # Z galerií
    gallery_matches = re.findall(r'\[gallery[^\]]*ids="([^"]+)"[^\]]*\]', post_content)
    for match in gallery_matches:
        ids = [id.strip() for id in match.split(',') if id.strip().isdigit()]
        image_ids.update(map(int, ids))
    
    # Featured image
    if current_thumbnail and current_thumbnail.isdigit():
        image_ids.add(int(current_thumbnail))
    
    print(f"📊 Nalezeno {len(image_ids)} obrázků k opravě")
    
    replacements = []
    
    for img_id in image_ids:
        # Získat informace o obrázku
        mysql_cursor.execute(f"""
            SELECT guid FROM {TBL_WP_POSTS} WHERE ID = %s AND post_type = 'attachment'
        """, (img_id,))
        
        result = mysql_cursor.fetchone()
        if not result:
            continue
            
        current_url = result[0]
        current_filename = os.path.basename(current_url)
        
        # Najít největší verzi
        largest = finder.find_largest_version(img_id, current_filename, f"{article_unique_id}/dummy")
        
        if largest:
            current_size = finder.extract_dimensions(current_filename)
            largest_size = (largest['width'], largest['height'])
            
            print(f"\n🔍 Obrázek ID {img_id}:")
            print(f"   📏 Současný: {current_size[0] if current_size[0] else '?'}x{current_size[1] if current_size[1] else '?'}")
            print(f"   🎯 Největší: {largest_size[0]}x{largest_size[1]}")
            print(f"   📝 Nahradím ID {img_id} -> {largest['wp_id']}")
            
            replacements.append({
                'old_id': img_id,
                'new_id': largest['wp_id'],
                'old_filename': current_filename,
                'new_filename': largest['filename'],
                'improvement': f"{current_size} -> {largest_size}"
            })
        else:
            print(f"\n⚠️  Obrázek ID {img_id}: Už je největší dostupná verze")
    
    if not replacements:
        print("\n✅ Všechny obrázky už používají největší dostupné verze")
        return True
    
    print(f"\n🎯 APLIKUJI {len(replacements)} NÁHRAD:")
    
    # Aplikovat náhrady
    updated_content = post_content
    featured_updated = False
    
    for replacement in replacements:
        old_id = replacement['old_id']
        new_id = replacement['new_id']
        
        # Nahradit v obsahu
        updated_content = re.sub(rf'\b{old_id}\b', str(new_id), updated_content)
        
        # Nahradit featured image
        if current_thumbnail and int(current_thumbnail) == old_id:
            mysql_cursor.execute(f"""
                UPDATE {TBL_WP_POSTMETA} 
                SET meta_value = %s 
                WHERE post_id = %s AND meta_key = '_thumbnail_id'
            """, (new_id, post_id))
            featured_updated = True
            print(f"   🎯 Featured image: {old_id} -> {new_id}")
    
    # Uložit aktualizovaný obsah
    mysql_cursor.execute(f"""
        UPDATE {TBL_WP_POSTS} 
        SET post_content = %s 
        WHERE ID = %s
    """, (updated_content, post_id))
    
    print(f"\n✅ OPRAVA DOKONČENA:")
    print(f"   📝 Obsah aktualizován: {len(replacements)} náhrad")
    if featured_updated:
        print(f"   🎯 Featured image aktualizován")
    
    return True

def main():
    """Hlavní funkce"""
    
    # ID článku z debug výstupu
    article_unique_id = "665510acb770a"
    article_title = "Přeměňte svou zahradu na stylový relaxační koutek"
    
    print("=" * 80)
    print(f"🔧 OPRAVA OBRÁZKŮ - POUŽITÍ NEJVĚTŠÍCH DOSTUPNÝCH VERZÍ")
    print("=" * 80)
    print(f"📖 Článek: {article_title}")
    print(f"🆔 Unique ID: {article_unique_id}")
    print()
    
    # Načíst mapování
    logging.info("Načítám mapování obrázků...")
    image_map = load_mapping('image_map.json')
    logging.info(f"Načteno {len(image_map)} mapování obrázků")
    
    # Připojit k databázi
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Inicializovat finder
        finder = LargestImageFinder(image_map)
        
        # Opravit článek
        success = fix_article_with_largest_versions(article_unique_id, mysql_cursor, finder)
        
        if success:
            mysql_conn.commit()
            print(f"\n🎉 OPRAVA ÚSPĚŠNĚ DOKONČENA!")
            print(f"🔍 Doporučuji spustit: python debug_specific_article.py \"{article_title}\"")
        else:
            print(f"\n❌ OPRAVA SE NEZDAŘILA")
            
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Chyba: {e}")
        print(f"\n💥 CHYBA: {e}")
    finally:
        mysql_cursor.close()
        mysql_conn.close()

if __name__ == "__main__":
    main()
