import logging
import os
import mysql.connector
from datetime import datetime
import re
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import (
    TBL_CLANEK, TBL_OBRAZEK,
    TBL_WP_POSTS, TBL_WP_POSTMETA,
    OLD_IMAGE_BASE_PATH, WP_UPLOADS_PATH, WP_SITE_URL,
    DEFAULT_WP_USER_ID
)
from utils_mujdum import (
    load_mapping, save_mapping,
    get_mime_type, format_wp_datetime, format_wp_datetime_gmt
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def migrate_images():
    """
    Migruje obrázky z databáze mujdum-2 do WordPress BEZ kopírování souborů.
    
    PŘEDPOKLAD: Složka s obrázky je již zkopírována do wp-content/uploads/obrazek/
    
    Tento skript pouze:
    1. Vytvoří záznamy v wp_posts pro každý obrázek
    2. Nastaví správné cesty (obrazek/původní_cesta) 
    3. Vytvoří mapování pro další skripty
    """
    logging.info("Spouštím migraci obrázků pro databázi mujdum-2 (bez kopírování souborů)...")
    logging.info("PŘEDPOKLAD: Složka s obrázky je zkopírována do wp-content/uploads/obrazek/")
    
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()

    image_map = load_mapping('image_map.json')
    processed_paths = set(image_map.keys())
    new_images_count = 0
    failed_images_count = 0

    try:
        # 1. Získat cesty k obrázkům z článků
        pg_cursor.execute(f"SELECT id_clanek, obrazek_src, cas_vlozeni FROM {TBL_CLANEK} WHERE obrazek_src IS NOT NULL AND obrazek_src != ''")
        article_images = [(row[0], row[1], row[2]) for row in pg_cursor.fetchall()]
        logging.info(f"Nalezeno {len(article_images)} obrázků v článcích.")

        # 2. Získat cesty k obrázkům z tabulky obrazek
        pg_cursor.execute(f"""
            SELECT id_obrazek, soubor, polozka_id 
            FROM {TBL_OBRAZEK} 
            WHERE soubor IS NOT NULL AND soubor != ''
        """)
        db_images = [(row[0], row[1], row[2]) for row in pg_cursor.fetchall()]
        logging.info(f"Nalezeno {len(db_images)} obrázků v tabulce {TBL_OBRAZEK}.")
        
        # Výpis statistik podle polozka_id
        polozka_counts = {}
        for _, _, polozka_id in db_images:
            if polozka_id:
                polozka_counts[polozka_id] = polozka_counts.get(polozka_id, 0) + 1
        
        gallery_articles = len([count for count in polozka_counts.values() if count > 1])
        logging.info(f"Nalezeno {gallery_articles} článků s více obrázky (potenciální galerie).")

        # 3. Zpracovat obrázky z článků (featured images)
        for article_id, image_src, created_date in article_images:
            if image_src in processed_paths:
                continue

            # Cesta v WordPress: obrazek/původní_cesta
            wp_image_path = f"obrazek/{image_src}"
            
            # Kontrola, zda soubor existuje v cílové lokaci
            full_wp_path = os.path.join(WP_UPLOADS_PATH, wp_image_path)
            if not os.path.exists(full_wp_path):
                logging.warning(f"Soubor '{full_wp_path}' neexistuje ve WordPress uploads, přeskakuji.")
                failed_images_count += 1
                continue

            try:
                # Získat MIME typ
                mime_type = get_mime_type(full_wp_path)
                
                # Připravit data pro wp_posts
                post_date = format_wp_datetime(created_date or datetime.now())
                post_date_gmt = format_wp_datetime_gmt(created_date or datetime.now())
                
                filename = os.path.basename(image_src)
                post_title = os.path.splitext(filename)[0].replace('-', ' ').replace('_', ' ').title()
                post_name = os.path.splitext(filename)[0].lower()
                guid = f"{WP_SITE_URL}/wp-content/uploads/{wp_image_path}"
                
                # Vložit do wp_posts
                sql_posts = f"""
                    INSERT INTO {TBL_WP_POSTS}
                    (post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt,
                    post_status, comment_status, ping_status, post_name, post_modified, post_modified_gmt,
                    post_parent, guid, menu_order, post_type, post_mime_type, comment_count,
                    to_ping, pinged, post_content_filtered)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                mysql_cursor.execute(sql_posts, (
                    DEFAULT_WP_USER_ID, post_date, post_date_gmt, '', post_title, '',
                    'inherit', 'closed', 'closed', post_name, post_date, post_date_gmt,
                    0, guid, 0, 'attachment', mime_type, 0, '', '', ''
                ))
                attachment_id = mysql_cursor.lastrowid
                
                # Vložit metadata
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (attachment_id, '_wp_attached_file', wp_image_path)
                )
                
                # Označit zdroj migrace
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (attachment_id, 'sabre_source', 'article_image_mujdum2')
                )
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (attachment_id, 'sabre_article_id', str(article_id))
                )
                
                # Uložit mapování
                image_map[image_src] = {
                    'wp_id': attachment_id,
                    'wp_path': wp_image_path,
                    'wp_url': guid
                }
                processed_paths.add(image_src)
                new_images_count += 1
                
                mysql_conn.commit()
                logging.info(f"Obrázek '{image_src}' úspěšně migrován s ID: {attachment_id}")
                
            except Exception as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při vytváření záznamu pro obrázek '{image_src}': {e}")
                failed_images_count += 1

        # 4. Zpracovat obrázky z tabulky obrazek (galerie)
        for image_id, image_file, polozka_id in db_images:
            if image_file in processed_paths:
                continue
            
            # Cesta v WordPress: obrazek/polozka_id/soubor.jpg NEBO obrazek/soubor.jpg
            if polozka_id and polozka_id.strip():
                wp_image_path = f"obrazek/{polozka_id}/{image_file}"
            else:
                wp_image_path = f"obrazek/{image_file}"
            
            # Kontrola existence souboru - nejprve přesný název
            full_wp_path = os.path.join(WP_UPLOADS_PATH, wp_image_path)
            actual_file_path = None
            
            if os.path.exists(full_wp_path):
                actual_file_path = full_wp_path
                logging.debug(f"Soubor nalezen přesně: {wp_image_path}")
            else:
                # Soubor nenalezen přesně, hledáme podle vzoru
                import glob
                
                # Vzor: původní-název-HASH_rozměry.přípona nebo původní-název-HASH.přípona
                base_name = os.path.splitext(image_file)[0]
                extension = os.path.splitext(image_file)[1]
                
                # Hledat v polozka_id složce
                if polozka_id and polozka_id.strip():
                    search_dir = os.path.join(WP_UPLOADS_PATH, 'obrazek', polozka_id)
                    if os.path.exists(search_dir):
                        # Vzory pro hledání: název-HASH.ext a název-HASH_rozměry.ext
                        search_patterns = [
                            os.path.join(search_dir, f"{base_name}-*{extension}"),
                            os.path.join(search_dir, f"{base_name}*{extension}")
                        ]
                        
                        for pattern in search_patterns:
                            matches = glob.glob(pattern)
                            if matches:
                                # Použít první nalezený soubor
                                actual_file_path = matches[0]
                                wp_image_path = os.path.relpath(actual_file_path, WP_UPLOADS_PATH)
                                logging.info(f"Soubor '{image_file}' nalezen jako: {wp_image_path}")
                                break
                
                # Pokud stále nenalezen, zkusit alternativní cestu (bez polozka_id)
                if not actual_file_path:
                    alt_search_dir = os.path.join(WP_UPLOADS_PATH, 'obrazek')
                    search_patterns = [
                        os.path.join(alt_search_dir, f"{base_name}-*{extension}"),
                        os.path.join(alt_search_dir, f"{base_name}*{extension}")
                    ]
                    
                    for pattern in search_patterns:
                        matches = glob.glob(pattern)
                        if matches:
                            actual_file_path = matches[0]
                            wp_image_path = os.path.relpath(actual_file_path, WP_UPLOADS_PATH)
                            logging.info(f"Soubor '{image_file}' nalezen alternativně jako: {wp_image_path}")
                            break
                
                # Pokud stále nenalezen
                if not actual_file_path:
                    logging.warning(f"Soubor '{image_file}' nenalezen ani podle vzoru pro polozka_id '{polozka_id}', přeskakuji.")
                    failed_images_count += 1
                    continue
                
                full_wp_path = actual_file_path
            
            try:
                mime_type = get_mime_type(full_wp_path)
                
                # Použít aktuální datum
                post_date = format_wp_datetime(datetime.now())
                post_date_gmt = format_wp_datetime_gmt(datetime.now())
                
                filename = os.path.basename(image_file)
                post_title = os.path.splitext(filename)[0].replace('-', ' ').replace('_', ' ').title()
                post_name = os.path.splitext(filename)[0].lower()
                guid = f"{WP_SITE_URL}/wp-content/uploads/{wp_image_path}"
                
                mysql_cursor.execute(sql_posts, (
                    DEFAULT_WP_USER_ID, post_date, post_date_gmt, '', post_title, '',
                    'inherit', 'closed', 'closed', post_name, post_date, post_date_gmt,
                    0, guid, 0, 'attachment', mime_type, 0, '', '', ''
                ))
                attachment_id = mysql_cursor.lastrowid
                
                # Metadata
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (attachment_id, '_wp_attached_file', wp_image_path)
                )
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (attachment_id, 'sabre_source', 'obrazek_table_mujdum2')
                )
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (attachment_id, 'sabre_polozka_id', polozka_id or '')
                )
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (attachment_id, 'sabre_obrazek_id', str(image_id))
                )
                
                # Mapování podle polozka_id/soubor PRO správné nalezení v galeriích
                if polozka_id and polozka_id.strip():
                    full_key = f"{polozka_id}/{image_file}"
                    image_map[full_key] = {
                        'wp_id': attachment_id,
                        'wp_path': wp_image_path,
                        'wp_url': guid
                    }
                    processed_paths.add(full_key)
                
                # Také mapování jen podle názvu souboru pro fallback
                image_map[image_file] = {
                    'wp_id': attachment_id,
                    'wp_path': wp_image_path,
                    'wp_url': guid
                }
                processed_paths.add(image_file)
                new_images_count += 1
                
                mysql_conn.commit()
                logging.info(f"Obrázek '{image_file}' úspěšně migrován s ID: {attachment_id}")
                
            except Exception as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při vytváření záznamu pro obrázek '{image_file}': {e}")
                failed_images_count += 1

    except Exception as e:
        logging.error(f"Obecná chyba v migrate_images: {e}")
    finally:
        # Uložení mapování
        save_mapping(image_map, 'image_map.json')
        logging.info(f"Mapování úspěšně uloženo, počet záznamů: {len(image_map)}")
        
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        logging.info(f"Migrace obrázků pro mujdum-2 dokončena. Zpracováno celkem {new_images_count} nových obrázků. Selhalo: {failed_images_count}.")
        
        print()
        print("=" * 60)
        print("📁 INSTRUKCE PO MIGRACI OBRÁZKŮ:")
        print("=" * 60)
        print(f"1. Zkopírujte složku s obrázky do:")
        print(f"   {WP_UPLOADS_PATH}/obrazek/")
        print()
        print("2. Ujistěte se, že WordPress má práva ke čtení:")
        print("   sudo chown -R www-data:www-data wp-content/uploads/obrazek/")
        print("   sudo chmod -R 755 wp-content/uploads/obrazek/")
        print()
        print("3. Obrázky budou dostupné na URL:")
        print(f"   {WP_SITE_URL}/wp-content/uploads/obrazek/...")
        print("=" * 60)

if __name__ == "__main__":
    migrate_images()
