#!/usr/bin/env python3
"""
Debug konkrétní polozka_id pro zjištění kde jsou soubory
"""
import os
import logging
from db_connectors import get_pg_connection
from config_mujdum import TBL_OBRAZEK, WP_UPLOADS_PATH

def debug_specific_polozka():
    """Zjistí kde jsou soubory pro konkrétní polozka_id"""
    
    print("=" * 70)
    print("🔍 DEBUG KONKRÉTNÍ POLOZKA_ID")
    print("=" * 70)
    
    # Problematická polozka_id z debug výstupu
    test_polozka_ids = ['654fe53159338', '65540c71a0c83']
    
    obrazek_base = os.path.join(WP_UPLOADS_PATH, 'obrazek')
    
    pg_conn = get_pg_connection()
    pg_cursor = pg_conn.cursor()
    
    for polozka_id in test_polozka_ids:
        print(f"\n🔍 POLOZKA_ID: {polozka_id}")
        print("-" * 40)
        
        # 1. Najít soubory v databázi pro tuto polozka_id
        pg_cursor.execute(f"""
            SELECT id_obrazek, soubor, typ, priorita
            FROM {TBL_OBRAZEK} 
            WHERE polozka_id = %s
            ORDER BY priorita DESC
        """, (polozka_id,))
        
        db_files = pg_cursor.fetchall()
        print(f"📊 Databáze obsahuje {len(db_files)} souborů:")
        for img_id, soubor, typ, priorita in db_files:
            print(f"   {soubor} (typ: {typ}, priorita: {priorita})")
        
        # 2. Zkontrolovat očekávané cesty
        print(f"\n📁 Kontrola očekávaných cest:")
        
        # Očekávaná složka s polozka_id
        expected_dir = os.path.join(obrazek_base, polozka_id)
        print(f"   Očekávaná složka: {expected_dir}")
        if os.path.exists(expected_dir):
            files_in_dir = os.listdir(expected_dir)
            print(f"   ✅ Složka existuje s {len(files_in_dir)} soubory:")
            for file in files_in_dir[:5]:
                print(f"      {file}")
            if len(files_in_dir) > 5:
                print(f"      ... a dalších {len(files_in_dir) - 5}")
        else:
            print(f"   ❌ Složka neexistuje!")
        
        # 3. Hledat soubory všude v obrazek/
        print(f"\n🔍 Hledání souborů všude v obrazek/:")
        for img_id, soubor, typ, priorita in db_files[:3]:  # jen první 3
            print(f"   Hledám: {soubor}")
            found_paths = []
            
            for root, dirs, files in os.walk(obrazek_base):
                if soubor in files:
                    rel_path = os.path.relpath(os.path.join(root, soubor), WP_UPLOADS_PATH)
                    found_paths.append(rel_path)
            
            if found_paths:
                print(f"      ✅ Nalezen v:")
                for path in found_paths:
                    print(f"         {path}")
            else:
                print(f"      ❌ Nenalezen!")
    
    # 4. Najít vzorové polozka_id které fungují
    print(f"\n4. VZOROVÉ FUNGUJÍCÍ POLOZKA_ID:")
    print("-" * 40)
    
    # Najít první existující složky
    subdirs = [d for d in os.listdir(obrazek_base) if os.path.isdir(os.path.join(obrazek_base, d))]
    print(f"📂 Zkouším první 3 existující složky:")
    
    for subdir in subdirs[:3]:
        subdir_path = os.path.join(obrazek_base, subdir)
        files_in_subdir = [f for f in os.listdir(subdir_path) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif'))]
        
        print(f"\n   📁 {subdir}/ ({len(files_in_subdir)} obrázků)")
        
        # Zkontrolovat, zda tento polozka_id existuje v databázi
        pg_cursor.execute(f"""
            SELECT COUNT(*) FROM {TBL_OBRAZEK} 
            WHERE polozka_id = %s
        """, (subdir,))
        db_count = pg_cursor.fetchone()[0]
        
        if db_count > 0:
            print(f"      ✅ Polozka_id {subdir} má {db_count} záznamů v DB")
            # Ukázat vzorové soubory
            for file in files_in_subdir[:3]:
                print(f"         {file}")
        else:
            print(f"      ❌ Polozka_id {subdir} NENÍ v databázi!")
    
    pg_cursor.close()
    pg_conn.close()
    
    print(f"\n5. ZÁVĚR:")
    print("-" * 40)
    print("🎯 Na základě tohoto debug bude jasné:")
    print("   1. Zda jsou soubory organizované podle polozka_id")
    print("   2. Nebo zda jsou v jiné strukture")
    print("   3. Jak upravit logiku hledání v migraci")

if __name__ == "__main__":
    debug_specific_polozka()
