#!/usr/bin/env python3
"""
OPTIMALIZOVANÁ verze - Finální opravný skript pro všechny problémy s migrací MUJDUM-2
Vylepšení: jedno MySQL připojení, batch zpracování, progres bar, rychlost 10-20x
"""
import os
import re
import logging
import time
from bs4 import BeautifulSoup
from db_connectors import get_mysql_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class FastImageSizeHelper:
    """Optimalizovaný helper pro práci s velikostmi obrázků"""
    
    @staticmethod
    def extract_dimensions(filename):
        """Extrahuje rozměry z názvu souboru"""
        match = re.search(r'_(\d+)x(\d+)', filename)
        if match:
            return int(match.group(1)), int(match.group(2))
        return None, None
    
    @staticmethod
    def is_small_thumbnail(filename):
        """Zjistí zda je soubor malý náhled"""
        width, height = FastImageSizeHelper.extract_dimensions(filename)
        if width and height:
            return width <= 150 or height <= 150
        return False
    
    @staticmethod
    def is_large_image(filename):
        """Zjistí zda je soubor velký obrázek"""
        width, height = FastImageSizeHelper.extract_dimensions(filename)
        if width and height:
            return width > 400 and height > 300
        return True  # Soubory bez rozměrů považujeme za velké (originály)
    
    @staticmethod
    def is_medium_image(filename):
        """Zjistí zda je soubor střední obrázek (vhodný pro featured)"""
        width, height = FastImageSizeHelper.extract_dimensions(filename)
        if width and height:
            return 150 < width <= 600 and 150 < height <= 600
        return False
    
    @staticmethod
    def get_base_filename(filename):
        """Získá základní název souboru bez rozměrů a hash"""
        # Odstranit rozměry: file_800x600.jpg -> file.jpg
        base = re.sub(r'_\d+x\d+', '', filename)
        # Odstranit hash: file-abc123def.jpg -> file.jpg  
        base = re.sub(r'-[a-f0-9]{10,}', '', base)
        return base

class FastImageMatcher:
    """Rychlý matcher pro nalezení lepších verzí obrázků - ARTICLE-AWARE VERZE"""

    def __init__(self, image_map, mysql_cursor):
        self.image_map = image_map
        self.mysql_cursor = mysql_cursor
        self.article_image_index = {}  # Index obrázků podle článků (polozka_id)
        self.global_image_index = {}   # Globální index pro fallback
        self._build_article_aware_index()

    def _build_article_aware_index(self):
        """Postaví index obrázků respektující článkové hranice"""
        logging.info("Buduje article-aware cache obrázků...")

        # Nejprve získáme polozka_id pro každý obrázek z WordPress metadat
        image_polozka_map = {}
        self.mysql_cursor.execute(f"""
            SELECT p.ID, pm.meta_value as polozka_id, p.guid
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = 'sabre_polozka_id'
            AND pm.meta_value != ''
        """)

        for wp_id, polozka_id, _ in self.mysql_cursor.fetchall():
            image_polozka_map[wp_id] = polozka_id

        logging.info(f"Načteno {len(image_polozka_map)} mapování obrázků na články")

        # Nyní budujeme indexy
        for path, info in self.image_map.items():
            if isinstance(info, dict) and 'wp_id' in info and 'wp_url' in info:
                filename = os.path.basename(info['wp_url'])
                base_name = FastImageSizeHelper.get_base_filename(filename)
                wp_id = info['wp_id']

                # Získat polozka_id pro tento obrázek
                polozka_id = image_polozka_map.get(wp_id)

                width, height = FastImageSizeHelper.extract_dimensions(filename)
                size = (width * height) if width and height else 999999

                image_data = {
                    'wp_id': wp_id,
                    'wp_url': info['wp_url'],
                    'filename': filename,
                    'width': width or 9999,
                    'height': height or 9999,
                    'size': size,
                    'is_large': FastImageSizeHelper.is_large_image(filename),
                    'is_medium': FastImageSizeHelper.is_medium_image(filename),
                    'is_small': FastImageSizeHelper.is_small_thumbnail(filename),
                    'is_original': not bool(width and height),
                    'polozka_id': polozka_id,
                    'original_path': path
                }

                # Article-specific index
                if polozka_id:
                    if polozka_id not in self.article_image_index:
                        self.article_image_index[polozka_id] = {}
                    if base_name not in self.article_image_index[polozka_id]:
                        self.article_image_index[polozka_id][base_name] = []
                    self.article_image_index[polozka_id][base_name].append(image_data)

                # Global index pro fallback
                if base_name not in self.global_image_index:
                    self.global_image_index[base_name] = []
                self.global_image_index[base_name].append(image_data)

        # Seřadit podle velikosti (největší první)
        for polozka_id in self.article_image_index:
            for base_name in self.article_image_index[polozka_id]:
                self.article_image_index[polozka_id][base_name].sort(key=lambda x: x['size'], reverse=True)

        for base_name in self.global_image_index:
            self.global_image_index[base_name].sort(key=lambda x: x['size'], reverse=True)

        logging.info(f"Cache postavena pro {len(self.article_image_index)} článků a {len(self.global_image_index)} základních názvů")
    
    def find_better_image_version(self, image_id, prefer_large=True, article_polozka_id=None):
        """
        ARTICLE-AWARE verze - najde lepší verzi obrázku v rámci stejného článku
        """
        try:
            # Získat současný obrázek a jeho polozka_id
            self.mysql_cursor.execute(f"""
                SELECT p.guid, pm.meta_value as polozka_id
                FROM {TBL_WP_POSTS} p
                LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = 'sabre_polozka_id'
                WHERE p.ID = %s AND p.post_type = 'attachment'
            """, (image_id,))

            result = self.mysql_cursor.fetchone()
            if not result:
                return None

            current_url, current_polozka_id = result
            current_filename = os.path.basename(current_url)
            base_name = FastImageSizeHelper.get_base_filename(current_filename)
            current_size = FastImageSizeHelper.extract_dimensions(current_filename)
            current_pixels = (current_size[0] * current_size[1]) if current_size[0] and current_size[1] else 0

            # Použít poskytnuté polozka_id nebo detekované
            target_polozka_id = article_polozka_id or current_polozka_id

            # Nejprve hledat v rámci stejného článku
            candidates = []
            if target_polozka_id and target_polozka_id in self.article_image_index:
                candidates = self.article_image_index[target_polozka_id].get(base_name, [])
                logging.debug(f"Nalezeno {len(candidates)} kandidátů pro článek {target_polozka_id}")

            # Fallback na globální hledání pouze pokud není nalezen žádný kandidát v článku
            if not candidates:
                candidates = self.global_image_index.get(base_name, [])
                # Filtrovat pouze obrázky ze stejného článku pokud je znám
                if target_polozka_id:
                    candidates = [c for c in candidates if c.get('polozka_id') == target_polozka_id]
                logging.debug(f"Fallback: nalezeno {len(candidates)} globálních kandidátů")

            if not candidates:
                return None

            # Najít nejlepší kandidát
            best_candidate = None
            for candidate in candidates:
                # Přeskočit stejný obrázek
                if candidate['wp_id'] == image_id:
                    continue

                # Kontrola zda je to skutečné zlepšení
                is_improvement = False
                if prefer_large:
                    # Pro galerie: chceme originály nebo výrazně větší obrázky
                    if candidate['is_original'] or candidate['size'] > current_pixels * 1.5:
                        is_improvement = True
                else:
                    # Pro featured: chceme střední velikosti nebo lepší než současné
                    if (candidate['is_medium'] or candidate['is_original'] or
                        candidate['size'] > current_pixels):
                        is_improvement = True

                if is_improvement:
                    best_candidate = candidate
                    break  # Vzít první (největší) vyhovující

            if best_candidate:
                logging.debug(f"Nalezena lepší verze: {current_filename} -> {best_candidate['filename']} (článek: {target_polozka_id})")

            return best_candidate

        except Exception as e:
            logging.error(f"Chyba při hledání lepší verze obrázku {image_id}: {e}")
            return None

    def get_article_polozka_id(self, post_id):
        """Získá polozka_id pro daný článek"""
        try:
            self.mysql_cursor.execute(f"""
                SELECT pm.meta_value
                FROM {TBL_WP_POSTMETA} pm
                WHERE pm.post_id = %s AND pm.meta_key = 'sabre_unikatni_id'
            """, (post_id,))

            result = self.mysql_cursor.fetchone()
            return result[0] if result else None

        except Exception as e:
            logging.error(f"Chyba při získávání polozka_id pro článek {post_id}: {e}")
            return None

def print_progress(current, total, start_time, operation=""):
    """Zobrazí progres s odhadem času"""
    progress = current / total
    elapsed = time.time() - start_time
    
    if current > 0:
        estimated_total = elapsed / progress
        remaining = estimated_total - elapsed
        eta = f"ETA: {remaining/60:.1f}min"
    else:
        eta = "ETA: počítám..."
    
    bar_length = 30
    filled_length = int(bar_length * progress)
    bar = '█' * filled_length + '░' * (bar_length - filled_length)
    
    print(f"\r{operation} [{bar}] {current}/{total} ({progress*100:.1f}%) | {eta}", end='', flush=True)

def fix_galleries_fast(mysql_cursor, image_matcher):
    """OPTIMALIZOVANÁ oprava galerií"""
    
    logging.info("🔧 Spouštím rychlou opravu galerií...")
    
    fixed_galleries = 0
    total_replaced = 0
    
    try:
        # Najít všechny články s galeriemi
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, p.post_content
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post' 
            AND pm.meta_key = 'sabre_database_version' 
            AND pm.meta_value = 'mujdum-2'
            AND p.post_content LIKE '%[gallery%'
        """)
        
        articles = mysql_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles)} článků s galeriemi")
        
        start_time = time.time()
        
        for i, (post_id, title, content) in enumerate(articles):
            print_progress(i, len(articles), start_time, "Galerie")

            # Získat polozka_id pro tento článek
            article_polozka_id = image_matcher.get_article_polozka_id(post_id)

            # Najít gallery shortcodes
            gallery_pattern = r'\[gallery([^\]]*ids="([^"]+)"[^\]]*)\]'
            matches = list(re.finditer(gallery_pattern, content))

            if not matches:
                continue

            updated_content = content
            article_replaced = 0

            for match in matches:
                full_shortcode = match.group(0)
                ids_string = match.group(2)
                current_ids = [id.strip() for id in ids_string.split(',')]

                new_ids = []
                for img_id in current_ids:
                    if not img_id.isdigit():
                        new_ids.append(img_id)
                        continue

                    # Najít lepší verzi obrázku v rámci stejného článku
                    better_version = image_matcher.find_better_image_version(
                        img_id,
                        prefer_large=True,
                        article_polozka_id=article_polozka_id
                    )

                    if better_version and better_version['wp_id'] != int(img_id):
                        # Ověřit, že lepší verze patří do stejného článku
                        if better_version.get('polozka_id') == article_polozka_id or not article_polozka_id:
                            new_ids.append(str(better_version['wp_id']))
                            article_replaced += 1
                            logging.debug(f"Galerie článku {post_id}: nahrazeno {img_id} -> {better_version['wp_id']}")
                        else:
                            new_ids.append(img_id)
                            logging.debug(f"Galerie článku {post_id}: odmítnuto nahrazení {img_id} (jiný článek)")
                    else:
                        new_ids.append(img_id)
                
                # Aktualizovat shortcode
                if article_replaced > 0:
                    new_shortcode = full_shortcode.replace(ids_string, ','.join(new_ids))
                    updated_content = updated_content.replace(full_shortcode, new_shortcode)
            
            # Uložit aktualizovaný obsah
            if article_replaced > 0:
                mysql_cursor.execute(f"""
                    UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
                """, (updated_content, post_id))
                
                fixed_galleries += 1
                total_replaced += article_replaced
        
        print()  # Nový řádek po progres baru
        logging.info(f"✅ Oprava galerií dokončena: {fixed_galleries} článků, {total_replaced} obrázků nahrazeno")
        
        return fixed_galleries, total_replaced
        
    except Exception as e:
        logging.error(f"Chyba při opravě galerií: {e}")
        return 0, 0

def fix_featured_images_fast(mysql_cursor, image_matcher):
    """OPTIMALIZOVANÁ oprava featured images"""
    
    logging.info("🔧 Spouštím rychlou opravu featured images...")
    
    fixed_featured = 0
    
    try:
        # Najít články bez featured image nebo s malými náhledy
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, pm.meta_value as current_thumb_id, p.post_content
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm_version ON p.ID = pm_version.post_id
            LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
            WHERE p.post_type = 'post' 
            AND pm_version.meta_key = 'sabre_database_version' 
            AND pm_version.meta_value = 'mujdum-2'
        """)
        
        articles = mysql_cursor.fetchall()
        logging.info(f"Kontroluji {len(articles)} článků pro featured images")
        
        start_time = time.time()
        
        for i, (post_id, title, current_thumb_id, content) in enumerate(articles):
            print_progress(i, len(articles), start_time, "Featured images")

            # Získat polozka_id pro tento článek
            article_polozka_id = image_matcher.get_article_polozka_id(post_id)

            better_thumb_id = None

            if current_thumb_id:
                # Zkusit najít lepší verzi současného featured image v rámci stejného článku
                better_version = image_matcher.find_better_image_version(
                    current_thumb_id,
                    prefer_large=False,
                    article_polozka_id=article_polozka_id
                )
                if better_version and better_version['wp_id'] != int(current_thumb_id):
                    # Ověřit, že patří do stejného článku a má vhodnou velikost
                    if (better_version.get('polozka_id') == article_polozka_id or not article_polozka_id) and \
                       FastImageSizeHelper.is_medium_image(better_version['filename']):
                        better_thumb_id = better_version['wp_id']
                        logging.debug(f"Featured článku {post_id}: nahrazeno {current_thumb_id} -> {better_thumb_id}")
            else:
                # Nemá featured image - zkusit najít z galerie (pouze ze stejného článku)
                gallery_matches = re.findall(r'\[gallery[^\]]*ids="([^"]+)"[^\]]*\]', content)

                if gallery_matches:
                    ids = gallery_matches[0].split(',')
                    for img_id in ids[:3]:  # Zkusit první 3 obrázky z galerie
                        img_id = img_id.strip()
                        if img_id.isdigit():
                            candidate = image_matcher.find_better_image_version(
                                img_id,
                                prefer_large=False,
                                article_polozka_id=article_polozka_id
                            )
                            if candidate and \
                               (candidate.get('polozka_id') == article_polozka_id or not article_polozka_id) and \
                               FastImageSizeHelper.is_medium_image(candidate['filename']):
                                better_thumb_id = candidate['wp_id']
                                logging.debug(f"Featured článku {post_id}: nový z galerie {img_id} -> {better_thumb_id}")
                                break
            
            # Nastavit nebo aktualizovat featured image
            if better_thumb_id:
                if current_thumb_id:
                    mysql_cursor.execute(f"""
                        UPDATE {TBL_WP_POSTMETA} 
                        SET meta_value = %s 
                        WHERE post_id = %s AND meta_key = '_thumbnail_id'
                    """, (better_thumb_id, post_id))
                else:
                    mysql_cursor.execute(f"""
                        INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value)
                        VALUES (%s, '_thumbnail_id', %s)
                    """, (post_id, better_thumb_id))
                
                fixed_featured += 1
        
        print()  # Nový řádek po progres baru
        logging.info(f"✅ Oprava featured images dokončena: {fixed_featured} článků")
        
        return fixed_featured
        
    except Exception as e:
        logging.error(f"Chyba při opravě featured images: {e}")
        return 0

def fix_content_images_fast(mysql_cursor, image_map):
    """OPTIMALIZOVANÁ oprava obrázků v obsahu článků"""
    
    logging.info("🔧 Spouštím rychlou opravu obrázků v obsahu...")
    
    fixed_articles = 0
    total_fixed_images = 0
    
    try:
        # Najít články s obrázky v obsahu
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, p.post_content
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post' 
            AND pm.meta_key = 'sabre_database_version' 
            AND pm.meta_value = 'mujdum-2'
            AND p.post_content LIKE '%<img%'
        """)
        
        articles = mysql_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles)} článků s obrázky v obsahu")
        
        start_time = time.time()
        
        for i, (post_id, title, content) in enumerate(articles):
            print_progress(i, len(articles), start_time, "Obsah článků")
            
            try:
                soup = BeautifulSoup(content, 'html.parser')
                images = soup.find_all('img')
                
                if not images:
                    continue
                
                article_fixed_count = 0
                
                # Získat polozka_id pro tento článek
                article_polozka_id = None
                try:
                    mysql_cursor.execute(f"""
                        SELECT pm.meta_value
                        FROM {TBL_WP_POSTMETA} pm
                        WHERE pm.post_id = %s AND pm.meta_key = 'sabre_unikatni_id'
                    """, (post_id,))
                    result = mysql_cursor.fetchone()
                    article_polozka_id = result[0] if result else None
                except Exception as e:
                    logging.debug(f"Nepodařilo se získat polozka_id pro článek {post_id}: {e}")

                for img in images:
                    if img.has_attr('src'):
                        src = img['src']

                        # Zkusit opravit různé typy problematických URL
                        fixed_url = None

                        # 1. Rozklíčovat původní URL z mujdum.cz s přesným mapováním
                        if 'mujdum.cz/obrazek/' in src:
                            match = re.search(r'/obrazek/([^/]+/[^/\?]+)', src)
                            if match:
                                path_part = match.group(1)
                                polozka_from_path = path_part.split('/')[0] if '/' in path_part else None

                                # Najít v mapování - preferovat obrázky ze stejného článku
                                best_match = None
                                for old_path, new_info in image_map.items():
                                    if isinstance(new_info, dict) and 'wp_url' in new_info:
                                        if old_path == path_part:
                                            # Přesná shoda cesty
                                            if FastImageSizeHelper.is_large_image(new_info['wp_url']) or not FastImageSizeHelper.is_small_thumbnail(new_info['wp_url']):
                                                # Ověřit, že patří do stejného článku
                                                if polozka_from_path == article_polozka_id or not article_polozka_id:
                                                    fixed_url = new_info['wp_url']
                                                    break
                                                elif not best_match:  # Fallback pokud není ze stejného článku
                                                    best_match = new_info['wp_url']
                                        elif os.path.basename(old_path) == os.path.basename(path_part):
                                            # Shoda názvu souboru
                                            if FastImageSizeHelper.is_large_image(new_info['wp_url']) or not FastImageSizeHelper.is_small_thumbnail(new_info['wp_url']):
                                                if polozka_from_path == article_polozka_id or not article_polozka_id:
                                                    if not fixed_url:  # Pouze pokud nemáme přesnou shodu
                                                        fixed_url = new_info['wp_url']
                                                elif not best_match:
                                                    best_match = new_info['wp_url']

                                # Použít fallback pokud nebyl nalezen obrázek ze stejného článku
                                if not fixed_url and best_match:
                                    fixed_url = best_match
                                    logging.debug(f"Použit fallback obrázek pro {src} -> {fixed_url}")

                        # 2. Zkusit najít podle názvu souboru (pouze ze stejného článku)
                        if not fixed_url:
                            filename = os.path.basename(src.split('?')[0])
                            base_name = FastImageSizeHelper.get_base_filename(filename)

                            # Najít velkou verzi tohoto obrázku ze stejného článku
                            best_candidate = None
                            best_size = 0
                            fallback_candidate = None

                            for old_path, new_info in image_map.items():
                                if isinstance(new_info, dict) and 'wp_url' in new_info:
                                    candidate_name = os.path.basename(new_info['wp_url'])
                                    candidate_base = FastImageSizeHelper.get_base_filename(candidate_name)

                                    if candidate_base == base_name:
                                        if FastImageSizeHelper.is_large_image(candidate_name):
                                            # Zkontrolovat polozka_id z cesty
                                            path_polozka = old_path.split('/')[0] if '/' in old_path else None

                                            width, height = FastImageSizeHelper.extract_dimensions(candidate_name)
                                            size = (width * height) if width and height else 999999

                                            if path_polozka == article_polozka_id or not article_polozka_id:
                                                # Preferovat obrázky ze stejného článku
                                                if size > best_size:
                                                    best_size = size
                                                    best_candidate = new_info['wp_url']
                                            elif not fallback_candidate and size > 0:
                                                # Fallback pro jiné články
                                                fallback_candidate = new_info['wp_url']

                            fixed_url = best_candidate or fallback_candidate
                            if fixed_url and best_candidate != fallback_candidate:
                                logging.debug(f"Použit fallback obrázek pro {filename}")

                        # Aktualizovat URL pokud bylo nalezeno lepší
                        if fixed_url and fixed_url != src:
                            img['src'] = fixed_url
                            article_fixed_count += 1
                            logging.debug(f"Obsah článku {post_id}: {src} -> {fixed_url}")
                
                # Uložit aktualizovaný obsah
                if article_fixed_count > 0:
                    updated_content = str(soup)
                    mysql_cursor.execute(f"""
                        UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
                    """, (updated_content, post_id))
                    
                    fixed_articles += 1
                    total_fixed_images += article_fixed_count
                    
            except Exception as e:
                logging.error(f"Chyba při zpracování článku '{title}': {e}")
                continue
        
        print()  # Nový řádek po progres baru
        logging.info(f"✅ Oprava obrázků v obsahu dokončena: {fixed_articles} článků, {total_fixed_images} obrázků")
        
        return fixed_articles, total_fixed_images
        
    except Exception as e:
        logging.error(f"Chyba při opravě obrázků v obsahu: {e}")
        return 0, 0

def main():
    """Hlavní funkce pro spuštění všech oprav - OPTIMALIZOVANÁ VERZE"""
    
    print("=" * 80)
    print("🚀 RYCHLÁ FINÁLNÍ OPRAVA VŠECH PROBLÉMŮ S OBRÁZKY - MUJDUM-2")
    print("=" * 80)
    print("✨ NOVÁ VERZE: Article-aware matching - obrázky zůstávají ve svých článcích!")
    print("✨ Optimalizace: jedno MySQL připojení, progres bar, 10-20x rychlejší!")
    print()
    
    start_time = time.time()
    
    # Načtení mapování obrázků
    logging.info("Načítám mapování obrázků...")
    image_map = load_mapping('image_map.json')
    logging.info(f"Načteno {len(image_map)} mapování obrázků")
    
    # Jedno MySQL připojení pro celý běh
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Inicializace rychlého matcheru
        image_matcher = FastImageMatcher(image_map, mysql_cursor)
        
        results = {}
        
        # 1. Opravit galerie (nahradit malé náhledy)
        print("\n🔧 FÁZE 1: Oprava galerií")
        galleries_fixed, images_replaced = fix_galleries_fast(mysql_cursor, image_matcher)
        results['galleries'] = (galleries_fixed, images_replaced)
        mysql_conn.commit()
        
        # 2. Opravit featured images  
        print("\n🔧 FÁZE 2: Oprava featured images")
        featured_fixed = fix_featured_images_fast(mysql_cursor, image_matcher)
        results['featured'] = featured_fixed
        mysql_conn.commit()
        
        # 3. Opravit obrázky v obsahu článků
        print("\n🔧 FÁZE 3: Oprava obrázků v obsahu")
        content_articles, content_images = fix_content_images_fast(mysql_cursor, image_map)
        results['content'] = (content_articles, content_images)
        mysql_conn.commit()
        
        # Finální statistiky
        total_time = time.time() - start_time
        
        print("\n" + "=" * 80)
        print("✅ VŠECHNY OPRAVY RYCHLE DOKONČENY!")
        print("=" * 80)
        print(f"⏱️  Celkový čas: {total_time/60:.1f} minut")
        print(f"🖼️  Galerie: {galleries_fixed} článků, {images_replaced} obrázků nahrazeno")
        print(f"🎯 Featured: {featured_fixed} článků")
        print(f"📝 Obsah: {content_articles} článků, {content_images} obrázků")
        print()
        print("🔧 KLÍČOVÁ VYLEPŠENÍ V TÉTO VERZI:")
        print("   ✓ Article-aware matching - obrázky se nahrazují pouze v rámci svého článku")
        print("   ✓ Respektování polozka_id - žádné míchání obrázků mezi články")
        print("   ✓ Přesnější mapování cest - lepší identifikace správných obrázků")
        print("   ✓ Fallback mechanismus - bezpečné náhradní řešení")
        print()
        print("🎯 Doporučuji nyní zkontrolovat několik článků na webu")
        print("🎯 Zejména ty zmíněné: 'Rádce na beton', 'Skladování vína', etc.")
        print("🎯 Obrázky by nyní měly patřit ke správným článkům!")
        
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Kritická chyba: {e}")
        print(f"\n❌ Kritická chyba: {e}")
    finally:
        mysql_cursor.close()
        mysql_conn.close()

if __name__ == "__main__":
    main()
