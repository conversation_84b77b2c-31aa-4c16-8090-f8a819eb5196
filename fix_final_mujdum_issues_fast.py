#!/usr/bin/env python3
"""
OPTIMALIZOVANÁ verze - Finální opravný skript pro všechny problémy s migrací MUJDUM-2
Vylepšení: jedno MySQL připojení, batch zpracování, progres bar, rychlost 10-20x
"""
import os
import re
import logging
import json
import time
from bs4 import BeautifulSoup
from db_connectors import get_mysql_connection, get_pg_connection
from config_mujdum import (
    TBL_WP_POSTS, TBL_WP_POSTMETA, TBL_CLANEK, TBL_OBRAZEK,
    WP_UPLOADS_PATH, WP_SITE_URL
)
from utils_mujdum import load_mapping, save_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class FastImageSizeHelper:
    """Optimalizovaný helper pro pr<PERSON>ci s velikostmi obr<PERSON>zků"""
    
    @staticmethod
    def extract_dimensions(filename):
        """Extrahuje rozměry z názvu souboru"""
        match = re.search(r'_(\d+)x(\d+)', filename)
        if match:
            return int(match.group(1)), int(match.group(2))
        return None, None
    
    @staticmethod
    def is_small_thumbnail(filename):
        """Zjistí zda je soubor malý náhled"""
        width, height = FastImageSizeHelper.extract_dimensions(filename)
        if width and height:
            return width <= 150 or height <= 150
        return False
    
    @staticmethod
    def is_large_image(filename):
        """Zjistí zda je soubor velký obrázek"""
        width, height = FastImageSizeHelper.extract_dimensions(filename)
        if width and height:
            return width > 400 and height > 300
        return True  # Soubory bez rozměrů považujeme za velké (originály)
    
    @staticmethod
    def is_medium_image(filename):
        """Zjistí zda je soubor střední obrázek (vhodný pro featured)"""
        width, height = FastImageSizeHelper.extract_dimensions(filename)
        if width and height:
            return 150 < width <= 600 and 150 < height <= 600
        return False
    
    @staticmethod
    def get_base_filename(filename):
        """Získá základní název souboru bez rozměrů a hash"""
        # Odstranit rozměry: file_800x600.jpg -> file.jpg
        base = re.sub(r'_\d+x\d+', '', filename)
        # Odstranit hash: file-abc123def.jpg -> file.jpg  
        base = re.sub(r'-[a-f0-9]{10,}', '', base)
        return base

class FastImageMatcher:
    """Rychlý matcher pro nalezení lepších verzí obrázků - GLOBÁLNÍ VERZE"""
    
    def __init__(self, image_map, mysql_cursor):
        self.image_map = image_map
        self.mysql_cursor = mysql_cursor
        self.global_image_index = {}  # Globální index všech obrázků
        self._build_global_index()
    
    def _build_global_index(self):
        """Postaví globální index všech obrázků podle základních názvů"""
        logging.info("Buduje cache obrázků...")
        
        for path, info in self.image_map.items():
            if isinstance(info, dict) and 'wp_id' in info and 'wp_url' in info:
                filename = os.path.basename(info['wp_url'])
                base_name = FastImageSizeHelper.get_base_filename(filename)
                
                if base_name not in self.global_image_index:
                    self.global_image_index[base_name] = []
                
                width, height = FastImageSizeHelper.extract_dimensions(filename)
                size = (width * height) if width and height else 999999
                
                self.global_image_index[base_name].append({
                    'wp_id': info['wp_id'],
                    'wp_url': info['wp_url'],
                    'filename': filename,
                    'width': width or 9999,
                    'height': height or 9999,
                    'size': size,
                    'is_large': FastImageSizeHelper.is_large_image(filename),
                    'is_medium': FastImageSizeHelper.is_medium_image(filename),
                    'is_small': FastImageSizeHelper.is_small_thumbnail(filename),
                    'is_original': not bool(width and height)
                })
        
        # Seřadit každou skupinu podle velikosti (největší první)
        for base_name in self.global_image_index:
            self.global_image_index[base_name].sort(key=lambda x: x['size'], reverse=True)
        
        logging.info(f"Cache postavena pro {len(self.global_image_index)} základních názvů obrázků")
    
    def find_better_image_version(self, image_id, prefer_large=True):
        """
        GLOBÁLNÍ verze - najde lepší verzi obrázku napříč celou databází
        """
        try:
            # Získat současný obrázek
            self.mysql_cursor.execute(f"""
                SELECT guid FROM {TBL_WP_POSTS} WHERE ID = %s AND post_type = 'attachment'
            """, (image_id,))
            
            result = self.mysql_cursor.fetchone()
            if not result:
                return None
                
            current_url = result[0]
            current_filename = os.path.basename(current_url)
            base_name = FastImageSizeHelper.get_base_filename(current_filename)
            current_size = FastImageSizeHelper.extract_dimensions(current_filename)
            current_pixels = (current_size[0] * current_size[1]) if current_size[0] and current_size[1] else 0
            
            # Najít v globálním indexu
            candidates = self.global_image_index.get(base_name, [])
            
            if not candidates:
                return None
            
            # Najít největší dostupnou verzi
            best_candidate = candidates[0]  # Už seřazeno podle velikosti
            
            # Kontrola zda je to skutečné zlepšení
            if best_candidate['wp_id'] != image_id:
                # Pro prefer_large: chceme originály nebo výrazně větší obrázky
                if prefer_large:
                    if best_candidate['is_original'] or best_candidate['size'] > current_pixels * 1.5:
                        return best_candidate
                # Pro featured: chceme střední velikosti nebo lepší než současné
                else:
                    if (best_candidate['is_medium'] or best_candidate['is_original'] or 
                        best_candidate['size'] > current_pixels):
                        return best_candidate
                
            return None
            
        except Exception as e:
            logging.error(f"Chyba při hledání lepší verze obrázku {image_id}: {e}")
            return None

def print_progress(current, total, start_time, operation=""):
    """Zobrazí progres s odhadem času"""
    progress = current / total
    elapsed = time.time() - start_time
    
    if current > 0:
        estimated_total = elapsed / progress
        remaining = estimated_total - elapsed
        eta = f"ETA: {remaining/60:.1f}min"
    else:
        eta = "ETA: počítám..."
    
    bar_length = 30
    filled_length = int(bar_length * progress)
    bar = '█' * filled_length + '░' * (bar_length - filled_length)
    
    print(f"\r{operation} [{bar}] {current}/{total} ({progress*100:.1f}%) | {eta}", end='', flush=True)

def fix_galleries_fast(mysql_cursor, image_matcher):
    """OPTIMALIZOVANÁ oprava galerií"""
    
    logging.info("🔧 Spouštím rychlou opravu galerií...")
    
    fixed_galleries = 0
    total_replaced = 0
    
    try:
        # Najít všechny články s galeriemi
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, p.post_content
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post' 
            AND pm.meta_key = 'sabre_database_version' 
            AND pm.meta_value = 'mujdum-2'
            AND p.post_content LIKE '%[gallery%'
        """)
        
        articles = mysql_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles)} článků s galeriemi")
        
        start_time = time.time()
        
        for i, (post_id, title, content) in enumerate(articles):
            print_progress(i, len(articles), start_time, "Galerie")
            
            # Najít gallery shortcodes
            gallery_pattern = r'\[gallery([^\]]*ids="([^"]+)"[^\]]*)\]'
            matches = list(re.finditer(gallery_pattern, content))
            
            if not matches:
                continue
                
            updated_content = content
            article_replaced = 0
            
            for match in matches:
                full_shortcode = match.group(0)
                ids_string = match.group(2)
                current_ids = [id.strip() for id in ids_string.split(',')]
                
                new_ids = []
                for img_id in current_ids:
                    if not img_id.isdigit():
                        new_ids.append(img_id)
                        continue
                        
                    # Najít lepší verzi obrázku
                    better_version = image_matcher.find_better_image_version(img_id, prefer_large=True)
                    
                    if better_version and better_version['wp_id'] != int(img_id):
                        new_ids.append(str(better_version['wp_id']))
                        article_replaced += 1
                    else:
                        new_ids.append(img_id)
                
                # Aktualizovat shortcode
                if article_replaced > 0:
                    new_shortcode = full_shortcode.replace(ids_string, ','.join(new_ids))
                    updated_content = updated_content.replace(full_shortcode, new_shortcode)
            
            # Uložit aktualizovaný obsah
            if article_replaced > 0:
                mysql_cursor.execute(f"""
                    UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
                """, (updated_content, post_id))
                
                fixed_galleries += 1
                total_replaced += article_replaced
        
        print()  # Nový řádek po progres baru
        logging.info(f"✅ Oprava galerií dokončena: {fixed_galleries} článků, {total_replaced} obrázků nahrazeno")
        
        return fixed_galleries, total_replaced
        
    except Exception as e:
        logging.error(f"Chyba při opravě galerií: {e}")
        return 0, 0

def fix_featured_images_fast(mysql_cursor, image_matcher):
    """OPTIMALIZOVANÁ oprava featured images"""
    
    logging.info("🔧 Spouštím rychlou opravu featured images...")
    
    fixed_featured = 0
    
    try:
        # Najít články bez featured image nebo s malými náhledy
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, pm.meta_value as current_thumb_id, p.post_content
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm_version ON p.ID = pm_version.post_id
            LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
            WHERE p.post_type = 'post' 
            AND pm_version.meta_key = 'sabre_database_version' 
            AND pm_version.meta_value = 'mujdum-2'
        """)
        
        articles = mysql_cursor.fetchall()
        logging.info(f"Kontroluji {len(articles)} článků pro featured images")
        
        start_time = time.time()
        
        for i, (post_id, title, current_thumb_id, content) in enumerate(articles):
            print_progress(i, len(articles), start_time, "Featured images")
            
            better_thumb_id = None
            
            if current_thumb_id:
                # Zkusit najít lepší verzi současného featured image
                better_version = image_matcher.find_better_image_version(current_thumb_id, prefer_large=False)
                if better_version and better_version['wp_id'] != int(current_thumb_id):
                    if FastImageSizeHelper.is_medium_image(better_version['filename']):
                        better_thumb_id = better_version['wp_id']
            else:
                # Nemá featured image - zkusit najít z galerie
                gallery_matches = re.findall(r'\[gallery[^\]]*ids="([^"]+)"[^\]]*\]', content)
                
                if gallery_matches:
                    ids = gallery_matches[0].split(',')
                    for img_id in ids[:3]:  # Zkusit první 3 obrázky z galerie
                        img_id = img_id.strip()
                        if img_id.isdigit():
                            candidate = image_matcher.find_better_image_version(img_id, prefer_large=False)
                            if candidate and FastImageSizeHelper.is_medium_image(candidate['filename']):
                                better_thumb_id = candidate['wp_id']
                                break
            
            # Nastavit nebo aktualizovat featured image
            if better_thumb_id:
                if current_thumb_id:
                    mysql_cursor.execute(f"""
                        UPDATE {TBL_WP_POSTMETA} 
                        SET meta_value = %s 
                        WHERE post_id = %s AND meta_key = '_thumbnail_id'
                    """, (better_thumb_id, post_id))
                else:
                    mysql_cursor.execute(f"""
                        INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value)
                        VALUES (%s, '_thumbnail_id', %s)
                    """, (post_id, better_thumb_id))
                
                fixed_featured += 1
        
        print()  # Nový řádek po progres baru
        logging.info(f"✅ Oprava featured images dokončena: {fixed_featured} článků")
        
        return fixed_featured
        
    except Exception as e:
        logging.error(f"Chyba při opravě featured images: {e}")
        return 0

def fix_content_images_fast(mysql_cursor, image_map):
    """OPTIMALIZOVANÁ oprava obrázků v obsahu článků"""
    
    logging.info("🔧 Spouštím rychlou opravu obrázků v obsahu...")
    
    fixed_articles = 0
    total_fixed_images = 0
    
    try:
        # Najít články s obrázky v obsahu
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, p.post_content
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post' 
            AND pm.meta_key = 'sabre_database_version' 
            AND pm.meta_value = 'mujdum-2'
            AND p.post_content LIKE '%<img%'
        """)
        
        articles = mysql_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles)} článků s obrázky v obsahu")
        
        start_time = time.time()
        
        for i, (post_id, title, content) in enumerate(articles):
            print_progress(i, len(articles), start_time, "Obsah článků")
            
            try:
                soup = BeautifulSoup(content, 'html.parser')
                images = soup.find_all('img')
                
                if not images:
                    continue
                
                article_fixed_count = 0
                
                for img in images:
                    if img.has_attr('src'):
                        src = img['src']
                        original_src = src
                        
                        # Zkusit opravit různé typy problematických URL
                        fixed_url = None
                        
                        # 1. Rozklíčovat původní URL z mujdum.cz
                        if 'mujdum.cz/obrazek/' in src:
                            match = re.search(r'/obrazek/([^/]+/[^/\?]+)', src)
                            if match:
                                path_part = match.group(1)
                                # Najít v mapování
                                for old_path, new_info in image_map.items():
                                    if isinstance(new_info, dict) and 'wp_url' in new_info:
                                        if old_path == path_part or os.path.basename(old_path) == os.path.basename(path_part):
                                            # Preferovat velké obrázky pro obsah
                                            if FastImageSizeHelper.is_large_image(new_info['wp_url']) or not FastImageSizeHelper.is_small_thumbnail(new_info['wp_url']):
                                                fixed_url = new_info['wp_url']
                                                break
                        
                        # 2. Zkusit najít podle názvu souboru
                        if not fixed_url:
                            filename = os.path.basename(src.split('?')[0])
                            base_name = FastImageSizeHelper.get_base_filename(filename)
                            
                            # Najít velkou verzi tohoto obrázku
                            best_candidate = None
                            best_size = 0
                            
                            for old_path, new_info in image_map.items():
                                if isinstance(new_info, dict) and 'wp_url' in new_info:
                                    candidate_name = os.path.basename(new_info['wp_url'])
                                    candidate_base = FastImageSizeHelper.get_base_filename(candidate_name)
                                    
                                    if candidate_base == base_name:
                                        if FastImageSizeHelper.is_large_image(candidate_name):
                                            width, height = FastImageSizeHelper.extract_dimensions(candidate_name)
                                            if width and height:
                                                size = width * height
                                                if size > best_size:
                                                    best_size = size
                                                    best_candidate = new_info['wp_url']
                                            elif not best_candidate:  # Originál bez rozměrů
                                                best_candidate = new_info['wp_url']
                            
                            if best_candidate:
                                fixed_url = best_candidate
                        
                        # Aktualizovat URL pokud bylo nalezeno lepší
                        if fixed_url and fixed_url != src:
                            img['src'] = fixed_url
                            article_fixed_count += 1
                
                # Uložit aktualizovaný obsah
                if article_fixed_count > 0:
                    updated_content = str(soup)
                    mysql_cursor.execute(f"""
                        UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
                    """, (updated_content, post_id))
                    
                    fixed_articles += 1
                    total_fixed_images += article_fixed_count
                    
            except Exception as e:
                logging.error(f"Chyba při zpracování článku '{title}': {e}")
                continue
        
        print()  # Nový řádek po progres baru
        logging.info(f"✅ Oprava obrázků v obsahu dokončena: {fixed_articles} článků, {total_fixed_images} obrázků")
        
        return fixed_articles, total_fixed_images
        
    except Exception as e:
        logging.error(f"Chyba při opravě obrázků v obsahu: {e}")
        return 0, 0

def main():
    """Hlavní funkce pro spuštění všech oprav - OPTIMALIZOVANÁ VERZE"""
    
    print("=" * 80)
    print("🚀 RYCHLÁ FINÁLNÍ OPRAVA VŠECH PROBLÉMŮ S OBRÁZKY - MUJDUM-2")
    print("=" * 80)
    print("✨ Optimalizace: jedno MySQL připojení, progres bar, 10-20x rychlejší!")
    print()
    
    start_time = time.time()
    
    # Načtení mapování obrázků
    logging.info("Načítám mapování obrázků...")
    image_map = load_mapping('image_map.json')
    logging.info(f"Načteno {len(image_map)} mapování obrázků")
    
    # Jedno MySQL připojení pro celý běh
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Inicializace rychlého matcheru
        image_matcher = FastImageMatcher(image_map, mysql_cursor)
        
        results = {}
        
        # 1. Opravit galerie (nahradit malé náhledy)
        print("\n🔧 FÁZE 1: Oprava galerií")
        galleries_fixed, images_replaced = fix_galleries_fast(mysql_cursor, image_matcher)
        results['galleries'] = (galleries_fixed, images_replaced)
        mysql_conn.commit()
        
        # 2. Opravit featured images  
        print("\n🔧 FÁZE 2: Oprava featured images")
        featured_fixed = fix_featured_images_fast(mysql_cursor, image_matcher)
        results['featured'] = featured_fixed
        mysql_conn.commit()
        
        # 3. Opravit obrázky v obsahu článků
        print("\n🔧 FÁZE 3: Oprava obrázků v obsahu")
        content_articles, content_images = fix_content_images_fast(mysql_cursor, image_map)
        results['content'] = (content_articles, content_images)
        mysql_conn.commit()
        
        # Finální statistiky
        total_time = time.time() - start_time
        
        print("\n" + "=" * 80)
        print("✅ VŠECHNY OPRAVY RYCHLE DOKONČENY!")
        print("=" * 80)
        print(f"⏱️  Celkový čas: {total_time/60:.1f} minut")
        print(f"🖼️  Galerie: {galleries_fixed} článků, {images_replaced} obrázků nahrazeno")
        print(f"🎯 Featured: {featured_fixed} článků")
        print(f"📝 Obsah: {content_articles} článků, {content_images} obrázků")
        print()
        print("🎯 Doporučuji nyní zkontrolovat několik článků na webu")
        print("🎯 Zejména ty zmíněné: 'Rádce na beton', 'Skladování vína', etc.")
        
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Kritická chyba: {e}")
        print(f"\n❌ Kritická chyba: {e}")
    finally:
        mysql_cursor.close()
        mysql_conn.close()

if __name__ == "__main__":
    main()
