import logging
import mysql.connector
import re
import os
from bs4 import BeautifulSoup
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import (
    TBL_CLANEK, TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA,
    TBL_WP_TERM_RELATIONSHIPS, DEFAULT_WP_USER_ID
)
from utils_mujdum import (
    load_mapping, save_mapping, generate_slug,
    format_wp_datetime, format_wp_datetime_gmt,
    extract_author_name, update_image_urls_in_content
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def migrate_articles(batch_size=50, start_offset=0):
    logging.info("Spouštím migraci článků pro databázi mujdum-2...")
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()

    # Načíst mapování
    category_map = load_mapping('category_map.json')
    user_map = load_mapping('user_map.json')
    image_map = load_mapping('image_map.json')
    article_map = load_mapping('article_map.json')

    # Počítadla
    new_articles_count = 0
    failed_articles_count = 0
    processed_articles = set(article_map.keys())

    try:
        # Získat celkový počet článků
        pg_cursor.execute(f"SELECT COUNT(*) FROM {TBL_CLANEK}")
        total_articles = pg_cursor.fetchone()[0]
        logging.info(f"Celkový počet článků v databázi: {total_articles}")

        # Zpracovat články po dávkách
        offset = start_offset
        while True:
            # POZOR: Odebrán sloupec 'zdroj' - neexistuje v databázi mujdum-2
            pg_cursor.execute(f"""
                SELECT id_clanek, unikatni_id, rubrika_id, nazev, nahled, text, 
                       zobrazovat_od, aktualizovano, cas_vlozeni, autor, obrazek_src, 
                       obrazek_alt, active_state, cislo_casopisu, nazev_galerie
                FROM {TBL_CLANEK}
                ORDER BY id_clanek
                LIMIT {batch_size} OFFSET {offset}
            """)
            
            articles = pg_cursor.fetchall()
            if not articles:
                break  # Konec dávek
            
            logging.info(f"Zpracovávám dávku {offset}-{offset + len(articles)} z {total_articles} článků.")
            
            for article in articles:
                (
                    id_clanek, unikatni_id, rubrika_id, nazev, nahled, text,
                    zobrazovat_od, aktualizovano, cas_vlozeni, autor, obrazek_src,
                    obrazek_alt, active_state, cislo_casopisu, nazev_galerie
                ) = article
                
                # Pokud už byl článek zpracován, přeskočit
                if str(id_clanek) in processed_articles:
                    logging.info(f"Článek ID {id_clanek} již byl zpracován, přeskakuji.")
                    continue
                
                try:
                    # 1. Připravit data pro WordPress post
                    
                    # Titulek
                    post_title = nazev or f"Článek {id_clanek}"
                    
                    # Slug
                    post_name = generate_slug(nazev)
                    
                    # Obsah
                    post_content = text or ""
                    
                    # Aktualizovat odkazy na obrázky v obsahu
                    post_content = update_image_urls_in_content(post_content, image_map)
                    
                    # Perex/excerpt
                    post_excerpt = nahled or ""
                    if post_excerpt:
                        # Odstranit HTML tagy z perexu
                        soup = BeautifulSoup(post_excerpt, 'html.parser')
                        post_excerpt = soup.get_text()
                    
                    # Datumy
                    post_date = format_wp_datetime(zobrazovat_od or cas_vlozeni)
                    post_date_gmt = format_wp_datetime_gmt(zobrazovat_od or cas_vlozeni)
                    post_modified = format_wp_datetime(aktualizovano or cas_vlozeni)
                    post_modified_gmt = format_wp_datetime_gmt(aktualizovano or cas_vlozeni)
                    
                    # Autor
                    post_author = DEFAULT_WP_USER_ID
                    if autor:
                        if autor in user_map:
                            post_author = user_map[autor]
                        else:
                            # Zkusit extrahovat jméno autora a najít mapování
                            author_name = extract_author_name(autor)
                            if author_name and author_name in user_map:
                                post_author = user_map[author_name]
                    
                    # Status
                    post_status = 'publish'
                    if active_state == 0:
                        post_status = 'draft'
                    
                    # 2. Vložit článek do WordPress
                    sql_posts = f"""
                        INSERT INTO {TBL_WP_POSTS}
                        (post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt,
                        post_status, comment_status, ping_status, post_name, post_modified, post_modified_gmt,
                        post_parent, guid, menu_order, post_type, post_mime_type, comment_count,
                        to_ping, pinged, post_content_filtered)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    mysql_cursor.execute(sql_posts, (
                        post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt,
                        post_status, 'open', 'open', post_name, post_modified, post_modified_gmt,
                        0, f"http://dumabyt.test/?p=", 0, 'post', '', 0,
                        '', '', ''  # Hodnoty pro to_ping, pinged, post_content_filtered
                    ))
                    wp_post_id = mysql_cursor.lastrowid
                    
                    # Aktualizovat GUID s ID
                    mysql_cursor.execute(f"UPDATE {TBL_WP_POSTS} SET guid = %s WHERE ID = %s", 
                                        (f"http://dumabyt.test/?p={wp_post_id}", wp_post_id))
                    
                    # 3. Přiřadit kategorii
                    if rubrika_id and str(rubrika_id) in category_map:
                        wp_term_id = category_map[str(rubrika_id)]
                        sql_term_rel = f"""
                            INSERT INTO {TBL_WP_TERM_RELATIONSHIPS}
                            (object_id, term_taxonomy_id, term_order)
                            VALUES (%s, %s, 0)
                        """
                        mysql_cursor.execute(sql_term_rel, (wp_post_id, wp_term_id))
                        logging.info(f"Článku ID {wp_post_id} přiřazena kategorie ID {wp_term_id}")
                    
                    # 4. Nastavit featured image
                    featured_attachment_id = None
                    
                    if obrazek_src:
                        # Zkusit najít podle obrazek_src
                        if obrazek_src in image_map:
                            featured_attachment_id = image_map[obrazek_src]['wp_id']
                            logging.debug(f"Nalezen featured image přímým mapováním: {obrazek_src}")
                        else:
                            # Zkusit podle názvu souboru a polozka_id
                            obrazek_filename = os.path.basename(obrazek_src)
                            for mapped_path, mapped_info in image_map.items():
                                if isinstance(mapped_info, dict) and 'wp_id' in mapped_info:
                                    if (mapped_path == obrazek_src or 
                                        mapped_path.endswith('/' + obrazek_filename) or
                                        os.path.basename(mapped_path) == obrazek_filename):
                                        featured_attachment_id = mapped_info['wp_id']
                                        logging.debug(f"Nalezen featured image: {mapped_path}")
                                        break
                    
                    # Pokud není featured image v obrazek_src, použít obrázek typu 0 s nejvyšší prioritou
                    if not featured_attachment_id and unikatni_id:
                        # Najít obrázek typu 0 (featured) v tabulce obrazek
                        pg_cursor.execute(f"""
                            SELECT soubor FROM {TBL_OBRAZEK}
                            WHERE polozka_id = %s AND active_state = 1 AND typ = 0
                            ORDER BY priorita DESC
                            LIMIT 1
                        """, (unikatni_id,))
                        featured_result = pg_cursor.fetchone()
                        
                        if featured_result:
                            featured_filename = featured_result[0]
                            logging.debug(f"Nalezen featured image typu 0: {featured_filename}")
                            
                            # Najít v mapování
                            for mapped_path, mapped_info in image_map.items():
                                if isinstance(mapped_info, dict) and 'wp_id' in mapped_info:
                                    if (os.path.basename(mapped_path) == featured_filename or
                                        mapped_path.endswith(f"/{featured_filename}") or
                                        mapped_path.endswith(f"{unikatni_id}/{featured_filename}")):
                                        featured_attachment_id = mapped_info['wp_id']
                                        logging.debug(f"Nalezen featured image typu 0 v mapování: {mapped_path}")
                                        break
                    
                    if featured_attachment_id:
                        sql_thumbnail = f"""
                            INSERT INTO {TBL_WP_POSTMETA}
                            (post_id, meta_key, meta_value)
                            VALUES (%s, %s, %s)
                        """
                        mysql_cursor.execute(sql_thumbnail, (wp_post_id, '_thumbnail_id', featured_attachment_id))
                        logging.info(f"Článku ID {wp_post_id} nastaven featured image ID {featured_attachment_id}")
                    else:
                        logging.warning(f"Featured image nebyl nalezen pro článek ID {id_clanek}")
                    
                    # 5. Uložit další metadata (bez 'zdroj' - neexistuje v mujdum-2)
                    
                    if cislo_casopisu:
                        mysql_cursor.execute(f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                                            (wp_post_id, 'sabre_cislo_casopisu', cislo_casopisu))
                    
                    # NOVÉ: Uložit název galerie (specifické pro mujdum-2)
                    if nazev_galerie:
                        mysql_cursor.execute(f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                                            (wp_post_id, 'sabre_nazev_galerie', nazev_galerie))
                    
                    # Uložit původní ID pro reference
                    mysql_cursor.execute(f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                                        (wp_post_id, 'sabre_id', id_clanek))
                    
                    if unikatni_id:
                        mysql_cursor.execute(f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                                            (wp_post_id, 'sabre_unikatni_id', unikatni_id))
                    
                    # Označit databázovou verzi pro rozlišení
                    mysql_cursor.execute(f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                                        (wp_post_id, 'sabre_database_version', 'mujdum-2'))
                    
                    # Commit transakce
                    mysql_conn.commit()
                    
                    # Aktualizovat mapování
                    article_map[str(id_clanek)] = wp_post_id
                    processed_articles.add(str(id_clanek))
                    new_articles_count += 1
                    
                    logging.info(f"Článek ID {id_clanek} úspěšně migrován s ID: {wp_post_id}")
                    
                except mysql.connector.Error as e:
                    mysql_conn.rollback()
                    logging.error(f"Chyba při vkládání článku ID {id_clanek} do WP: {e}")
                    failed_articles_count += 1
                except Exception as e:
                    mysql_conn.rollback()
                    logging.error(f"Obecná chyba při zpracování článku ID {id_clanek}: {e}")
                    failed_articles_count += 1
            
            # Uložit mapování po každé dávce
            save_mapping(article_map, 'article_map.json')
            
            # Posunout offset pro další dávku
            offset += batch_size
            
            logging.info(f"Dokončena dávka {offset-batch_size}-{offset}. Zpracováno {new_articles_count} nových článků.")
            
            # Volitelně: Přerušit po určitém počtu dávek pro testování
            # if offset > 1000:
            #     break

    except Exception as e:
        logging.error(f"Obecná chyba v migrate_articles: {e}")
    finally:
        save_mapping(article_map, 'article_map.json')
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        logging.info(f"Migrace článků pro mujdum-2 dokončena. Zpracováno celkem {new_articles_count} nových článků. Selhalo: {failed_articles_count}.")

if __name__ == "__main__":
    migrate_articles()
