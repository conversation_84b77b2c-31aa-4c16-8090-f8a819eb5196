#!/usr/bin/env python3
"""
Komplexní analýza konkrétního <PERSON> - porovnání p<PERSON>í <PERSON> vs WordPress
Článek: "Nemáte zahradu? Prostor pro relaxaci nabídne terasa, balkon nebo střecha"
"""
import json
import re
import os
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import TBL_CLANEK, TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA
from utils_mujdum import load_mapping

def analyze_original_database(article_title):
    """Analyzuje původní PostgreSQL databázi"""
    print("=" * 80)
    print("🔍 ANALÝZA PŮVODNÍ DATABÁZE (PostgreSQL)")
    print("=" * 80)
    
    pg_conn = get_pg_connection()
    pg_cursor = pg_conn.cursor()
    
    try:
        # 1. <PERSON><PERSON><PERSON><PERSON> v původní databázi
        pg_cursor.execute(f"""
            SELECT id_clanek, unikatni_id, nazev, obrazek_src, obrazek_alt, nazev_galerie, obsah
            FROM {TBL_CLANEK} 
            WHERE nazev ILIKE %s
        """, (f"%{article_title}%",))
        
        articles = pg_cursor.fetchall()
        
        if not articles:
            print(f"❌ Článek '{article_title}' nebyl nalezen v původní databázi!")
            return None
        
        article_data = {}
        
        for article_id, unique_id, title, featured_src, featured_alt, gallery_name, content in articles:
            print(f"📖 PŮVODNÍ ČLÁNEK:")
            print(f"   ID: {article_id}")
            print(f"   Název: {title}")
            print(f"   Unikátní ID (polozka_id): {unique_id}")
            print(f"   Featured image src: {featured_src}")
            print(f"   Featured image alt: {featured_alt}")
            print(f"   Název galerie: {gallery_name}")
            print(f"   Délka obsahu: {len(content) if content else 0} znaků")
            
            article_data = {
                'id': article_id,
                'unique_id': unique_id,
                'title': title,
                'featured_src': featured_src,
                'featured_alt': featured_alt,
                'gallery_name': gallery_name,
                'content': content
            }
            
            # 2. Najít všechny obrázky pro tento článek
            print(f"\n📊 PŮVODNÍ OBRÁZKY (polozka_id: {unique_id}):")
            
            pg_cursor.execute(f"""
                SELECT id_obrazek, soubor, popisek, priorita, typ, active_state, velikost_souboru
                FROM {TBL_OBRAZEK} 
                WHERE polozka_id = %s
                ORDER BY typ ASC, priorita DESC, id_obrazek ASC
            """, (unique_id,))
            
            images = pg_cursor.fetchall()
            
            featured_images = []
            gallery_images = []
            
            print(f"   Celkem nalezeno: {len(images)} obrázků")
            print()
            
            for img_id, filename, desc, priority, img_type, active, file_size in images:
                status = "✅ AKTIVNÍ" if active == 1 else "❌ NEAKTIVNÍ"
                type_name = "FEATURED" if img_type == 0 else "GALERIE" if img_type == 1 else f"TYP_{img_type}"
                
                print(f"   🖼️  ID: {img_id} | {type_name} | Priorita: {priority} | {status}")
                print(f"      📁 Soubor: {filename}")
                print(f"      📝 Popis: {desc}")
                print(f"      📏 Velikost: {file_size} bytes" if file_size else "      📏 Velikost: neznámá")
                print()
                
                img_data = {
                    'id': img_id,
                    'filename': filename,
                    'description': desc,
                    'priority': priority,
                    'type': img_type,
                    'active': active,
                    'file_size': file_size
                }
                
                if img_type == 0:
                    featured_images.append(img_data)
                elif img_type == 1:
                    gallery_images.append(img_data)
            
            article_data['featured_images'] = featured_images
            article_data['gallery_images'] = gallery_images
            
            # 3. Analyzovat obsah článku na obrázky
            if content:
                print(f"🔍 ANALÝZA OBSAHU ČLÁNKU:")
                
                # Najít odkazy na obrázky v obsahu
                image_patterns = [
                    r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>',
                    r'obrazek/([^/]+/[^"\'\s]+)',
                    r'mujdum\.cz/obrazek/([^"\'\s]+)'
                ]
                
                content_images = set()
                for pattern in image_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    content_images.update(matches)
                
                print(f"   Nalezeno {len(content_images)} odkazů na obrázky v obsahu:")
                for img_url in list(content_images)[:5]:  # Ukázat jen prvních 5
                    print(f"      🔗 {img_url}")
                
                article_data['content_images'] = list(content_images)
            
            break  # Zpracovat jen první nalezený článek
        
        pg_cursor.close()
        pg_conn.close()
        
        return article_data
        
    except Exception as e:
        print(f"❌ Chyba při analýze původní databáze: {e}")
        pg_cursor.close()
        pg_conn.close()
        return None

def analyze_wordpress_state(article_data):
    """Analyzuje současný stav ve WordPress"""
    print("\n" + "=" * 80)
    print("🔍 ANALÝZA SOUČASNÉHO STAVU (WordPress)")
    print("=" * 80)
    
    if not article_data:
        print("❌ Nelze analyzovat WordPress - chybí data z původní databáze")
        return None
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # 1. Najít článek ve WordPress podle unikatni_id
        unique_id = article_data['unique_id']
        
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, p.post_content, p.post_status
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post'
            AND pm.meta_key = 'sabre_unikatni_id'
            AND pm.meta_value = %s
        """, (unique_id,))
        
        wp_article = mysql_cursor.fetchone()
        
        if not wp_article:
            print(f"❌ Článek s unikatni_id '{unique_id}' nebyl nalezen ve WordPress!")
            mysql_cursor.close()
            mysql_conn.close()
            return None
        
        wp_post_id, wp_title, wp_content, wp_status = wp_article
        
        print(f"📖 WORDPRESS ČLÁNEK:")
        print(f"   Post ID: {wp_post_id}")
        print(f"   Název: {wp_title}")
        print(f"   Status: {wp_status}")
        print(f"   Délka obsahu: {len(wp_content) if wp_content else 0} znaků")
        
        wp_data = {
            'post_id': wp_post_id,
            'title': wp_title,
            'content': wp_content,
            'status': wp_status
        }
        
        # 2. Zkontrolovat featured image
        print(f"\n🖼️  FEATURED IMAGE ANALÝZA:")
        
        mysql_cursor.execute(f"""
            SELECT pm.meta_value as thumbnail_id
            FROM {TBL_WP_POSTMETA} pm
            WHERE pm.post_id = %s AND pm.meta_key = '_thumbnail_id'
        """, (wp_post_id,))
        
        featured_result = mysql_cursor.fetchone()
        
        if featured_result:
            thumbnail_id = featured_result[0]
            
            # Získat detaily featured image
            mysql_cursor.execute(f"""
                SELECT p.guid, p.post_title, pm1.meta_value as polozka_id, pm2.meta_value as attached_file
                FROM {TBL_WP_POSTS} p
                LEFT JOIN {TBL_WP_POSTMETA} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = 'sabre_polozka_id'
                LEFT JOIN {TBL_WP_POSTMETA} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_wp_attached_file'
                WHERE p.ID = %s AND p.post_type = 'attachment'
            """, (thumbnail_id,))
            
            featured_details = mysql_cursor.fetchone()
            
            if featured_details:
                featured_url, featured_title, featured_polozka_id, attached_file = featured_details
                
                print(f"   ✅ Featured image nastaven:")
                print(f"      ID: {thumbnail_id}")
                print(f"      URL: {featured_url}")
                print(f"      Název: {featured_title}")
                print(f"      Polozka ID: {featured_polozka_id}")
                print(f"      Soubor: {attached_file}")
                
                # Zkontrolovat velikost z názvu souboru
                filename = os.path.basename(featured_url)
                size_match = re.search(r'_(\d+)x(\d+)', filename)
                if size_match:
                    width, height = size_match.groups()
                    print(f"      📏 Rozměry: {width}x{height}")
                    
                    # Kontrola kvality
                    if int(width) <= 200 or int(height) <= 150:
                        print(f"      ⚠️  PROBLÉM: Malý náhled místo kvalitního obrázku!")
                    elif int(width) <= 400 or int(height) <= 300:
                        print(f"      ⚠️  VAROVÁNÍ: Střední velikost, možná by šel najít větší")
                    else:
                        print(f"      ✅ Dobrá velikost")
                else:
                    print(f"      ✅ Originál (bez rozměrů v názvu)")
                
                # Kontrola správnosti polozka_id
                if featured_polozka_id != unique_id:
                    print(f"      ⚠️  PROBLÉM: Featured image patří do jiného článku!")
                    print(f"         Očekáváno: {unique_id}")
                    print(f"         Skutečnost: {featured_polozka_id}")
                else:
                    print(f"      ✅ Featured image patří do správného článku")
                
                wp_data['featured_image'] = {
                    'id': thumbnail_id,
                    'url': featured_url,
                    'polozka_id': featured_polozka_id,
                    'filename': filename,
                    'size_match': size_match.groups() if size_match else None
                }
            else:
                print(f"   ❌ Featured image ID {thumbnail_id} nenalezen v attachments!")
                wp_data['featured_image'] = None
        else:
            print(f"   ❌ Featured image není nastaven!")
            wp_data['featured_image'] = None
        
        mysql_cursor.close()
        mysql_conn.close()
        
        return wp_data
        
    except Exception as e:
        print(f"❌ Chyba při analýze WordPress: {e}")
        mysql_cursor.close()
        mysql_conn.close()
        return None

def analyze_image_mapping(article_data):
    """Analyzuje mapování obrázků pro tento článek"""
    print("\n" + "=" * 80)
    print("🔍 ANALÝZA MAPOVÁNÍ OBRÁZKŮ")
    print("=" * 80)
    
    if not article_data:
        return None
    
    try:
        image_map = load_mapping('image_map.json')
        unique_id = article_data['unique_id']
        
        # Najít všechny obrázky pro tento článek v mapování
        article_images_in_map = {}
        
        for path, info in image_map.items():
            if isinstance(info, dict) and unique_id in path:
                filename = os.path.basename(info.get('wp_url', ''))
                
                # Analyzovat velikost
                size_match = re.search(r'_(\d+)x(\d+)', filename)
                is_thumbnail = bool(size_match)
                
                # Získat základní název
                base_name = re.sub(r'_\d+x\d+', '', filename)
                base_name = re.sub(r'-[a-f0-9]{10,}', '', base_name)
                
                if base_name not in article_images_in_map:
                    article_images_in_map[base_name] = {'originals': [], 'thumbnails': []}
                
                img_data = {
                    'path': path,
                    'wp_id': info.get('wp_id'),
                    'wp_url': info.get('wp_url'),
                    'filename': filename,
                    'is_thumbnail': is_thumbnail,
                    'size': size_match.groups() if size_match else None
                }
                
                if is_thumbnail:
                    article_images_in_map[base_name]['thumbnails'].append(img_data)
                else:
                    article_images_in_map[base_name]['originals'].append(img_data)
        
        print(f"📊 MAPOVÁNÍ PRO ČLÁNEK (polozka_id: {unique_id}):")
        print(f"   Nalezeno {len(article_images_in_map)} různých obrázků")
        print()
        
        # Analyzovat každý obrázek
        problems = []
        
        for base_name, versions in article_images_in_map.items():
            originals = versions['originals']
            thumbnails = versions['thumbnails']
            
            print(f"🖼️  {base_name}:")
            print(f"   Originály: {len(originals)}")
            print(f"   Náhledy: {len(thumbnails)}")
            
            # Ukázat originály
            for orig in originals:
                print(f"      ✅ Originál: ID {orig['wp_id']} - {orig['filename']}")
            
            # Ukázat náhledy
            for thumb in thumbnails:
                size_str = f"{thumb['size'][0]}x{thumb['size'][1]}" if thumb['size'] else "unknown"
                print(f"      📏 Náhled {size_str}: ID {thumb['wp_id']} - {thumb['filename']}")
            
            # Detekovat problémy
            if len(originals) == 0 and len(thumbnails) > 0:
                problems.append(f"Obrázek '{base_name}' má pouze náhledy, chybí originál")
            elif len(originals) > 1:
                problems.append(f"Obrázek '{base_name}' má více originálů ({len(originals)})")
            
            print()
        
        if problems:
            print(f"⚠️  DETEKOVANÉ PROBLÉMY:")
            for problem in problems:
                print(f"   - {problem}")
        else:
            print(f"✅ Žádné zjevné problémy v mapování")
        
        return article_images_in_map
        
    except Exception as e:
        print(f"❌ Chyba při analýze mapování: {e}")
        return None

def main():
    """Hlavní funkce pro analýzu konkrétního článku"""
    article_title = "Nemáte zahradu? Prostor pro relaxaci nabídne terasa, balkon nebo střecha"
    
    print("🔍 KOMPLEXNÍ ANALÝZA KONKRÉTNÍHO ČLÁNKU")
    print(f"📖 Článek: {article_title}")
    print()
    
    # 1. Analyzovat původní databázi
    original_data = analyze_original_database(article_title)
    
    # 2. Analyzovat WordPress stav
    wp_data = analyze_wordpress_state(original_data)
    
    # 3. Analyzovat mapování obrázků
    mapping_data = analyze_image_mapping(original_data)
    
    # 4. Souhrnná analýza a doporučení
    print("\n" + "=" * 80)
    print("📋 SOUHRNNÁ ANALÝZA A DOPORUČENÍ")
    print("=" * 80)
    
    if original_data and wp_data:
        print("✅ Článek byl úspěšně migrován do WordPress")
        
        # Porovnat featured images
        if original_data.get('featured_images') and wp_data.get('featured_image'):
            print("🖼️  Featured image:")
            print("   ✅ Nastaven ve WordPress")
            
            # Zkontrolovat kvalitu
            if wp_data['featured_image'].get('size_match'):
                width, height = wp_data['featured_image']['size_match']
                if int(width) <= 200 or int(height) <= 150:
                    print("   ⚠️  DOPORUČENÍ: Nahradit za větší verzi")
        
        # Uložit výsledky pro další zpracování
        results = {
            'original': original_data,
            'wordpress': wp_data,
            'mapping': mapping_data
        }
        
        with open('article_analysis_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Výsledky uloženy do article_analysis_results.json")
        print(f"📝 Nyní můžeme vytvořit cílený opravný skript")
    
    else:
        print("❌ Analýza nebyla úspěšná - chybí data")

if __name__ == "__main__":
    main()
