#!/usr/bin/env python3
"""
Reset skript pro migraci MUJDUM-2
Vymaže všechny migrované data z WordPress a resetuje mapování.
"""
import logging
import os
import mysql.connector
from db_connectors import get_mysql_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA, TBL_WP_TERMS, TBL_WP_TERM_TAXONOMY, TBL_WP_TERM_RELATIONSHIPS, MAPPINGS_DIR

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def reset_wordpress_data():
    """Vymaže všechny migrované <PERSON>, ob<PERSON><PERSON><PERSON><PERSON>, kate<PERSON>ie a metadata z WordPress."""
    logging.info("🧹 Spouštím reset WordPress dat...")
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # 1. Najít ID migravených článků
        logging.info("Hledám migrované <PERSON>...")
        mysql_cursor.execute(f"""
            SELECT DISTINCT post_id FROM {TBL_WP_POSTMETA} 
            WHERE meta_key = 'sabre_database_version' AND meta_value = 'mujdum-2'
        """)
        article_ids = [row[0] for row in mysql_cursor.fetchall()]
        logging.info(f"Nalezeno {len(article_ids)} migravených článků.")
        
        if article_ids:
            # Smazat metadata
            placeholders = ','.join(['%s'] * len(article_ids))
            mysql_cursor.execute(f"""
                DELETE FROM {TBL_WP_POSTMETA} WHERE post_id IN ({placeholders})
            """, article_ids)
            
            # Smazat články
            mysql_cursor.execute(f"""
                DELETE FROM {TBL_WP_POSTS} WHERE ID IN ({placeholders})
            """, article_ids)
            logging.info(f"Smazáno {len(article_ids)} migravených článků s metadata.")
        
        # 2. Najít ID migravených obrázků
        logging.info("Hledám migrované obrázky...")
        mysql_cursor.execute(f"""
            SELECT DISTINCT post_id FROM {TBL_WP_POSTMETA} 
            WHERE meta_key = 'sabre_source' AND meta_value LIKE '%mujdum2%'
        """)
        image_ids = [row[0] for row in mysql_cursor.fetchall()]
        logging.info(f"Nalezeno {len(image_ids)} migravených obrázků.")
        
        if image_ids:
            # Smazat metadata obrázků
            placeholders = ','.join(['%s'] * len(image_ids))
            mysql_cursor.execute(f"""
                DELETE FROM {TBL_WP_POSTMETA} WHERE post_id IN ({placeholders})
            """, image_ids)
            
            # Smazat obrázky
            mysql_cursor.execute(f"""
                DELETE FROM {TBL_WP_POSTS} WHERE ID IN ({placeholders})
            """, image_ids)
            logging.info(f"Smazáno {len(image_ids)} migravených obrázků s metadata.")
        
        # 3. Najít ID migravených kategorií
        logging.info("Hledám migrované kategorie...")
        mysql_cursor.execute(f"""
            SELECT DISTINCT tt.term_id 
            FROM {TBL_WP_TERM_TAXONOMY} tt
            WHERE tt.description LIKE '%sabre_id:%'
        """)
        category_term_ids = [row[0] for row in mysql_cursor.fetchall()]
        logging.info(f"Nalezeno {len(category_term_ids)} migravených kategorií.")
        
        if category_term_ids:
            # Najít term_taxonomy_id pro smazání vztahů
            placeholders = ','.join(['%s'] * len(category_term_ids))
            mysql_cursor.execute(f"""
                SELECT term_taxonomy_id FROM {TBL_WP_TERM_TAXONOMY} 
                WHERE term_id IN ({placeholders})
            """, category_term_ids)
            taxonomy_ids = [row[0] for row in mysql_cursor.fetchall()]
            
            if taxonomy_ids:
                # Smazat term relationships
                placeholders_tax = ','.join(['%s'] * len(taxonomy_ids))
                mysql_cursor.execute(f"""
                    DELETE FROM {TBL_WP_TERM_RELATIONSHIPS} 
                    WHERE term_taxonomy_id IN ({placeholders_tax})
                """, taxonomy_ids)
                
                # Smazat term taxonomy
                mysql_cursor.execute(f"""
                    DELETE FROM {TBL_WP_TERM_TAXONOMY} 
                    WHERE term_taxonomy_id IN ({placeholders_tax})
                """, taxonomy_ids)
            
            # Smazat terms
            mysql_cursor.execute(f"""
                DELETE FROM {TBL_WP_TERMS} 
                WHERE term_id IN ({placeholders})
            """, category_term_ids)
            
            logging.info(f"Smazáno {len(category_term_ids)} migravených kategorií.")
        
        # 4. Smazat orphaned metadata
        logging.info("Mažu orphaned metadata...")
        mysql_cursor.execute(f"""
            DELETE pm FROM {TBL_WP_POSTMETA} pm
            LEFT JOIN {TBL_WP_POSTS} p ON pm.post_id = p.ID
            WHERE p.ID IS NULL
        """)
        deleted_meta = mysql_cursor.rowcount
        logging.info(f"Smazáno {deleted_meta} orphaned metadata záznamů.")
        
        mysql_conn.commit()
        logging.info("✅ WordPress data úspěšně vymazána.")
        
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"❌ Chyba při mazání WordPress dat: {e}")
        raise
    finally:
        mysql_cursor.close()
        mysql_conn.close()

def reset_mappings():
    """Vymaže všechny mapovací soubory."""
    logging.info("🗑️ Resetuji mapovací soubory...")
    
    mapping_files = [
        'article_map.json',
        'category_map.json', 
        'image_map.json',
        'user_map.json',
        'gallery_map.json'
    ]
    
    deleted_count = 0
    for filename in mapping_files:
        filepath = os.path.join(MAPPINGS_DIR, filename)
        if os.path.exists(filepath):
            try:
                os.remove(filepath)
                logging.info(f"✅ Smazán mapovací soubor: {filename}")
                deleted_count += 1
            except Exception as e:
                logging.error(f"❌ Chyba při mazání {filename}: {e}")
        else:
            logging.info(f"ℹ️ Mapovací soubor {filename} neexistuje.")
    
    logging.info(f"🗑️ Smazáno {deleted_count} mapovacích souborů.")

def main():
    """Hlavní funkce pro reset."""
    print("=" * 60)
    print("🧹 RESET MIGRACE MUJDUM-2")
    print("=" * 60)
    print("⚠️  VAROVÁNÍ: Tato operace vymaže všechna migrovaná data!")
    print("   - Všechny migrované články")
    print("   - Všechny migrované obrázky (jen DB záznamy)")
    print("   - Všechny migrované kategorie")
    print("   - Všechny mapovací soubory")
    print()
    
    response = input("Pokračovat? (ano/ne): ").lower().strip()
    if response not in ['ano', 'a', 'yes', 'y']:
        print("❌ Reset zrušen.")
        return
    
    try:
        # Reset WordPress dat
        reset_wordpress_data()
        
        # Reset mapování
        reset_mappings()
        
        print()
        print("=" * 60)
        print("✅ RESET DOKONČEN!")
        print("=" * 60)
        print("📋 Co bylo provedeno:")
        print("   - Vymazány všechny migrované články z WordPress")
        print("   - Vymazány všechny migrované obrázky z WordPress DB")
        print("   - Vymazány všechny migrované kategorie")
        print("   - Vymazány všechny mapovací soubory")
        print()
        print("🚀 Nyní můžete spustit novou migraci:")
        print("   python run_migration_mujdum.py")
        print()
        print("💡 POZNÁMKA: Fyzické soubory obrázků v wp-content/uploads/obrazky/")
        print("   zůstávají nedotčené a jsou připravené pro novou migraci.")
        
    except Exception as e:
        print(f"❌ Chyba během resetu: {e}")
        print("🔧 Zkontrolujte logy pro více informací.")

if __name__ == "__main__":
    main()
