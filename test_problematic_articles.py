#!/usr/bin/env python3
"""
Test konkrétních problematický<PERSON> před a po opravě
"""
import logging
from debug_specific_article import debug_specific_article

def test_problematic_articles():
    """Testuje konkrétní články zmíněné uživatelem"""
    
    print("=" * 70)
    print("🧪 TEST PROBLEMATICKÝCH ČLÁNKŮ")
    print("=" * 70)
    
    problematic_articles = [
        "<PERSON><PERSON>d<PERSON> na beton, díl 7: <PERSON>š<PERSON><PERSON><PERSON>n<PERSON> betonu",
        "Skladování vína není jen tak", 
        "Interiér pod šikmým stropem",
        "Nemáte zahradu? Prostor pro relaxaci nabídne terasa, balkon nebo střecha"
    ]
    
    for article_title in problematic_articles:
        print(f"\n{'='*50}")
        print(f"Testování článku: {article_title}")
        print('='*50)
        
        try:
            debug_specific_article(article_title)
        except Exception as e:
            print(f"❌ Chyba při testování <PERSON> '{article_title}': {e}")
        
        print("\n" + "-"*50)
        input("Stiskněte Enter pro pokračování na další článek...")

if __name__ == "__main__":
    test_problematic_articles()
