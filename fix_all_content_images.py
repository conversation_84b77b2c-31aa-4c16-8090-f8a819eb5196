#!/usr/bin/env python3
"""
Univerzální opravný skript pro všechny články s problematickými obrázky v obsahu
Založeno na úspěšné logice z fix_sadrokarton_article.py
"""
import os
import re
import logging
from bs4 import BeautifulSoup
from db_connectors import get_mysql_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def fix_content_images_universal(content, image_map):
    """Univerzální oprava obrázků v obsahu - použije logiku z úspěšného skriptu"""
    
    if not content:
        return content, 0
    
    try:
        soup = BeautifulSoup(content, 'html.parser')
        images = soup.find_all('img')
        fixed_count = 0
        
        for img in images:
            if img.has_attr('src'):
                src = img['src']
                original_src = src
                new_url = None
                
                # Pokud má relativní cestu /obrazek/
                if src.startswith('/obrazek/'):
                    path_part = src[1:]  # Odstranit úvodní /
                    
                    # 1. Přesné mapování
                    if path_part in image_map and isinstance(image_map[path_part], dict):
                        new_url = image_map[path_part]['wp_url']
                    
                    # 2. Fallback podle názvu souboru (s odstranění rozměrů)
                    if not new_url:
                        filename = os.path.basename(path_part)
                        base_filename = re.sub(r'_\d+x\d+', '', filename)  # Odstranit rozměry
                        
                        # Extrahovat polozka_id z cesty
                        path_parts = path_part.split('/')
                        if len(path_parts) >= 2:
                            polozka_id = path_parts[1]  # obrazek/POLOZKA_ID/soubor.jpg
                            
                            # Hledat podle základního názvu s polozka_id
                            for mapped_path, mapped_info in image_map.items():
                                if isinstance(mapped_info, dict) and 'wp_url' in mapped_info:
                                    mapped_filename = os.path.basename(mapped_path)
                                    
                                    if (mapped_filename == base_filename and polozka_id in mapped_path):
                                        new_url = mapped_info['wp_url']
                                        break
                                    elif (mapped_filename == filename and polozka_id in mapped_path):
                                        new_url = mapped_info['wp_url']
                                        break
                            
                            # Fallback bez polozka_id
                            if not new_url:
                                for mapped_path, mapped_info in image_map.items():
                                    if isinstance(mapped_info, dict) and 'wp_url' in mapped_info:
                                        mapped_filename = os.path.basename(mapped_path)
                                        
                                        if mapped_filename == base_filename:
                                            new_url = mapped_info['wp_url']
                                            break
                                        elif mapped_filename == filename:
                                            new_url = mapped_info['wp_url']
                                            break
                
                # Aktualizovat URL
                if new_url and new_url != src:
                    img['src'] = new_url
                    fixed_count += 1
                    logging.debug(f"Opraven: {original_src} -> {new_url}")
        
        return str(soup), fixed_count
        
    except Exception as e:
        logging.error(f"Chyba při opravě obsahu: {e}")
        return content, 0

def fix_all_content_images():
    """Opraví obrázky v obsahu všech migrovaných článků"""
    
    print("=" * 70)
    print("🔧 UNIVERZÁLNÍ OPRAVA OBRÁZKŮ V OBSAHU - VŠECHNY ČLÁNKY")
    print("=" * 70)
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    image_map = load_mapping('image_map.json')
    
    fixed_articles = 0
    total_fixed_images = 0
    problematic_articles = 0
    
    try:
        # Najít všechny migrované články s problematickými cestami
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, p.post_content
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post' 
            AND pm.meta_key = 'sabre_database_version' 
            AND pm.meta_value = 'mujdum-2'
            AND p.post_content LIKE '%src="/obrazek/%'
        """)
        
        articles = mysql_cursor.fetchall()
        print(f"📊 Nalezeno {len(articles)} článků s problematickými obrázky")
        
        for post_id, title, content in articles:
            # Spočítat problematické obrázky
            problematic_count = len(re.findall(r'src="/obrazek/', content))
            if problematic_count > 0:
                problematic_articles += 1
                
                print(f"\n🔧 Opravuji článek: {title}")
                print(f"   📋 ID: {post_id} | Problematických obrázků: {problematic_count}")
                
                # Opravit obsah
                fixed_content, fixed_count = fix_content_images_universal(content, image_map)
                
                if fixed_count > 0:
                    # Uložit opravu
                    mysql_cursor.execute(f"""
                        UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
                    """, (fixed_content, post_id))
                    
                    fixed_articles += 1
                    total_fixed_images += fixed_count
                    print(f"   ✅ Opraveno {fixed_count} obrázků")
                else:
                    print(f"   ⚠️ Žádné obrázky neopraveny")
        
        mysql_conn.commit()
        
        print(f"\n{'='*70}")
        print(f"✅ OPRAVA DOKONČENA!")
        print(f"{'='*70}")
        print(f"📊 Statistiky:")
        print(f"   📋 Článků s problémy: {problematic_articles}")
        print(f"   ✅ Úspěšně opraveno: {fixed_articles} článků")
        print(f"   🖼️ Celkem opraveno obrázků: {total_fixed_images}")
        print(f"   📈 Úspěšnost: {fixed_articles/problematic_articles*100:.1f}%" if problematic_articles > 0 else "")
        
        return fixed_articles > 0
        
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Chyba při hromadné opravě: {e}")
        print(f"❌ Chyba: {e}")
        return False
    finally:
        mysql_cursor.close()
        mysql_conn.close()

def main():
    """Hlavní funkce"""
    
    print("🚀 Spouštím univerzální opravu obrázků v obsahu všech článků...")
    
    success = fix_all_content_images()
    
    if success:
        print("\n✅ Hromadná oprava byla úspěšná!")
        print("🎯 Doporučuji zkontrolovat několik článků na webu")
    else:
        print("\n❌ Hromadná oprava se nezdařila")

if __name__ == "__main__":
    main()
