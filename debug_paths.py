#!/usr/bin/env python3
"""
Debug skript pro zjištění skutečných cest obrázků
"""
import logging
import os
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import TBL_CLANEK, TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA, WP_UPLOADS_PATH

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_image_paths():
    """<PERSON>jist<PERSON>, jak vypadají cesty v originální databázi a ve WordPress uploads"""
    
    print("=" * 70)
    print("🔍 DEBUG CEST OBRÁZKŮ")
    print("=" * 70)
    
    # 1. Zkontrolovat originální databázi
    print("\n1. ORIGINÁLNÍ DATABÁZE (PostgreSQL):")
    print("-" * 40)
    
    pg_conn = get_pg_connection()
    pg_cursor = pg_conn.cursor()
    
    # Vzorové cesty z článků
    pg_cursor.execute(f"SELECT obrazek_src FROM {TBL_CLANEK} WHERE obrazek_src IS NOT NULL LIMIT 5")
    article_images = pg_cursor.fetchall()
    print("📄 Vzorové cesty z článků (obrazek_src):")
    for img in article_images:
        print(f"   {img[0]}")
    
    # Vzorové cesty z tabulky obrazek
    pg_cursor.execute(f"SELECT soubor, polozka_id FROM {TBL_OBRAZEK} WHERE soubor IS NOT NULL LIMIT 5")
    obrazek_images = pg_cursor.fetchall()
    print("\n🖼️ Vzorové cesty z tabulky obrazek:")
    for img, polozka in obrazek_images:
        print(f"   {img} (polozka_id: {polozka})")
    
    pg_cursor.close()
    pg_conn.close()
    
    # 2. Zkontrolovat WordPress uploads složku
    print(f"\n2. WORDPRESS UPLOADS SLOŽKA:")
    print("-" * 40)
    obrazek_path = os.path.join(WP_UPLOADS_PATH, 'obrazek')
    print(f"📁 Cesta: {obrazek_path}")
    
    if os.path.exists(obrazek_path):
        print("✅ Složka 'obrazek' existuje")
        
        # Zjistit strukturu složek
        subdirs = []
        sample_files = []
        
        for root, dirs, files in os.walk(obrazek_path):
            rel_path = os.path.relpath(root, obrazek_path)
            if rel_path != '.':
                subdirs.append(rel_path)
            
            # Vzít pár vzorových souborů
            for file in files[:2]:
                if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
                    rel_file_path = os.path.relpath(os.path.join(root, file), WP_UPLOADS_PATH)
                    sample_files.append(rel_file_path)
            
            if len(sample_files) >= 10:
                break
        
        print(f"\n📂 Nalezeno {len(subdirs)} podsložek:")
        for subdir in subdirs[:10]:
            print(f"   {subdir}")
        if len(subdirs) > 10:
            print(f"   ... a dalších {len(subdirs) - 10}")
        
        print(f"\n🖼️ Vzorové soubory (cesty pro WordPress):")
        for file_path in sample_files[:10]:
            print(f"   {file_path}")
        
    else:
        print("❌ Složka 'obrazek' neexistuje!")
        return
    
    # 3. Zkontrolovat WordPress databázi
    print(f"\n3. WORDPRESS DATABÁZE (MySQL):")
    print("-" * 40)
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    # Vzorové attachment záznamy
    mysql_cursor.execute(f"""
        SELECT p.ID, p.guid, pm.meta_value as attached_file
        FROM {TBL_WP_POSTS} p
        LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id 
        WHERE p.post_type = 'attachment' AND pm.meta_key = '_wp_attached_file'
        ORDER BY p.ID DESC LIMIT 5
    """)
    wp_attachments = mysql_cursor.fetchall()
    
    print("📎 Vzorové WordPress attachments:")
    for att_id, guid, attached_file in wp_attachments:
        print(f"   ID: {att_id}")
        print(f"   GUID: {guid}")
        print(f"   File: {attached_file}")
        print()
    
    # Featured images
    mysql_cursor.execute(f"""
        SELECT p.ID, p.post_title, pm.meta_value as thumbnail_id
        FROM {TBL_WP_POSTS} p
        LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id 
        WHERE p.post_type = 'post' AND pm.meta_key = '_thumbnail_id'
        LIMIT 3
    """)
    featured_images = mysql_cursor.fetchall()
    
    print("🖼️ Vzorové featured images:")
    for post_id, title, thumb_id in featured_images:
        print(f"   Post: {post_id} ({title[:50]}...)")
        print(f"   Thumbnail ID: {thumb_id}")
        print()
    
    mysql_cursor.close()
    mysql_conn.close()
    
    # 4. Recommendations
    print("4. DOPORUČENÍ PRO OPRAVU:")
    print("-" * 40)
    print("🔧 Na základě výše uvedených informací upravte:")
    print("   1. Cesty v migrate_images_mujdum_simple.py")
    print("   2. Mapování v utils_mujdum.py")
    print("   3. Featured image logiku v migrate_articles_mujdum.py")
    print("   4. Gallery shortcode v migrate_galleries_mujdum.py")

if __name__ == "__main__":
    debug_image_paths()
