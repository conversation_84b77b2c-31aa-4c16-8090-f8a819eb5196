#!/usr/bin/env python3
"""
OPRAVENÁ MIGRACE OBRÁZKŮ - Řeší kritick<PERSON> chyby původn<PERSON> migrace
- Preferuje origin<PERSON><PERSON> před náhledy
- Eliminuje cross-article contamination
- Respektuje priority z původní databáze
- Vytváří přesné mapování bez duplicit
"""
import os
import glob
import shutil
import logging
import re
from PIL import Image
import mysql.connector
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import (
    TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA,
    WP_UPLOADS_PATH, WP_SITE_URL, ORIGINAL_IMAGES_PATH
)
from utils_mujdum import load_mapping, save_mapping, get_mime_type

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_image_dimensions_and_size(file_path):
    """<PERSON><PERSON><PERSON><PERSON> rozměry a velikost obrázku"""
    try:
        with Image.open(file_path) as img:
            width, height = img.size
            file_size = os.path.getsize(file_path)
            return width, height, file_size
    except Exception as e:
        logging.warning(f"Nelze získat rozměry obrázku {file_path}: {e}")
        return None, None, os.path.getsize(file_path) if os.path.exists(file_path) else 0

def categorize_image_by_filename(filename):
    """Kategorizuje obrázek podle názvu souboru"""
    # Extrahovat rozměry z názvu
    size_match = re.search(r'_(\d+)x(\d+)', filename)
    
    if not size_match:
        return 'original', None, None
    
    width, height = map(int, size_match.groups())
    
    if width <= 150 or height <= 150:
        return 'small_thumbnail', width, height
    elif width <= 400 or height <= 400:
        return 'medium', width, height
    else:
        return 'large', width, height

def select_best_image_version(matches, prefer_original=True, min_size=200):
    """
    Vybere nejlepší verzi z nalezených souborů
    
    Args:
        matches: Seznam nalezených souborů
        prefer_original: Preferovat originály
        min_size: Minimální velikost pro thumbnaily
    
    Returns:
        Nejlepší soubor nebo None
    """
    if not matches:
        return None
    
    # Kategorizovat soubory
    categorized = {
        'original': [],
        'large': [],
        'medium': [],
        'small_thumbnail': []
    }
    
    for file_path in matches:
        filename = os.path.basename(file_path)
        category, width, height = categorize_image_by_filename(filename)
        
        # Získat skutečné rozměry a velikost
        real_width, real_height, file_size = get_image_dimensions_and_size(file_path)
        
        categorized[category].append({
            'path': file_path,
            'filename': filename,
            'width': real_width or width,
            'height': real_height or height,
            'file_size': file_size,
            'category': category
        })
    
    # Výběr podle priority
    if prefer_original and categorized['original']:
        # Seřadit originály podle velikosti souboru (největší první)
        categorized['original'].sort(key=lambda x: x['file_size'], reverse=True)
        return categorized['original'][0]['path']
    
    # Velké obrázky
    if categorized['large']:
        # Seřadit podle rozměrů (největší první)
        categorized['large'].sort(key=lambda x: (x['width'] or 0) * (x['height'] or 0), reverse=True)
        return categorized['large'][0]['path']
    
    # Střední obrázky (pouze pokud jsou dostatečně velké)
    suitable_medium = [img for img in categorized['medium'] 
                      if (img['width'] or 0) >= min_size and (img['height'] or 0) >= min_size]
    if suitable_medium:
        suitable_medium.sort(key=lambda x: (x['width'] or 0) * (x['height'] or 0), reverse=True)
        return suitable_medium[0]['path']
    
    # V nouzi i malé náhledy (ale logovat varování)
    if categorized['small_thumbnail']:
        logging.warning(f"Pouze malé náhledy dostupné pro: {matches[0]}")
        categorized['small_thumbnail'].sort(key=lambda x: (x['width'] or 0) * (x['height'] or 0), reverse=True)
        return categorized['small_thumbnail'][0]['path']
    
    # Fallback
    return matches[0]

def migrate_images_fixed(batch_size=100, start_offset=0):
    """Opravená migrace obrázků s kvalitní logikou výběru"""
    logging.info("🔧 Spouštím OPRAVENOU migraci obrázků...")
    
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()
    
    # Načíst existující mapování
    image_map = load_mapping('image_map.json')
    
    # Statistiky
    stats = {
        'processed': 0,
        'uploaded': 0,
        'skipped_existing': 0,
        'failed': 0,
        'originals_selected': 0,
        'thumbnails_selected': 0,
        'cross_contamination_prevented': 0
    }
    
    try:
        # Získat celkový počet obrázků
        pg_cursor.execute(f"SELECT COUNT(*) FROM {TBL_OBRAZEK}")
        total_images = pg_cursor.fetchone()[0]
        logging.info(f"Celkový počet obrázků: {total_images}")
        
        # Zpracovat po dávkách
        offset = start_offset
        
        while True:
            # Načíst dávku obrázků seřazenou podle polozka_id a priority
            pg_cursor.execute(f"""
                SELECT id_obrazek, polozka_id, soubor, popisek, priorita, typ, active_state
                FROM {TBL_OBRAZEK}
                WHERE active_state = 1
                ORDER BY polozka_id, typ ASC, priorita DESC, id_obrazek ASC
                LIMIT {batch_size} OFFSET {offset}
            """)
            
            images = pg_cursor.fetchall()
            if not images:
                break
            
            logging.info(f"Zpracovávám dávku {offset}-{offset + len(images)} z {total_images}")
            
            for image_data in images:
                id_obrazek, polozka_id, soubor, popisek, priorita, typ, active_state = image_data
                stats['processed'] += 1
                
                # Vytvořit unikátní klíč (POUZE s polozka_id - žádné duplicity!)
                if polozka_id and polozka_id.strip():
                    unique_key = f"{polozka_id.strip()}/{soubor}"
                else:
                    logging.warning(f"Obrázek ID {id_obrazek} nemá polozka_id - přeskakuji")
                    stats['failed'] += 1
                    continue
                
                # Zkontrolovat, zda už byl zpracován
                if unique_key in image_map:
                    logging.debug(f"Obrázek {unique_key} už byl zpracován")
                    stats['skipped_existing'] += 1
                    continue
                
                # Najít soubor v původním adresáři
                search_patterns = [
                    os.path.join(ORIGINAL_IMAGES_PATH, polozka_id, soubor),
                    os.path.join(ORIGINAL_IMAGES_PATH, polozka_id, f"*{soubor}*"),
                    os.path.join(ORIGINAL_IMAGES_PATH, polozka_id, f"{os.path.splitext(soubor)[0]}*"),
                ]
                
                found_files = []
                for pattern in search_patterns:
                    matches = glob.glob(pattern)
                    found_files.extend(matches)
                
                # Odstranit duplicity a seřadit
                found_files = list(set(found_files))
                
                if not found_files:
                    logging.warning(f"Soubor nenalezen: {unique_key}")
                    stats['failed'] += 1
                    continue
                
                # Vybrat nejlepší verzi
                prefer_original = (typ == 0)  # Featured images preferují originály
                min_size = 300 if typ == 0 else 200  # Vyšší požadavky na featured
                
                best_file = select_best_image_version(
                    found_files, 
                    prefer_original=prefer_original,
                    min_size=min_size
                )
                
                if not best_file:
                    logging.error(f"Nepodařilo se vybrat nejlepší verzi pro: {unique_key}")
                    stats['failed'] += 1
                    continue
                
                # Statistiky kvality
                filename = os.path.basename(best_file)
                category, _, _ = categorize_image_by_filename(filename)
                if category == 'original':
                    stats['originals_selected'] += 1
                else:
                    stats['thumbnails_selected'] += 1
                
                try:
                    # Vytvořit cílový adresář
                    target_dir = os.path.join(WP_UPLOADS_PATH, "obrazek", polozka_id)
                    os.makedirs(target_dir, exist_ok=True)
                    
                    # Generovat unikátní název
                    base_name = os.path.splitext(soubor)[0]
                    extension = os.path.splitext(soubor)[1]
                    
                    # Přidat hash pro unikátnost
                    import hashlib
                    import time
                    hash_suffix = hashlib.md5(f"{polozka_id}{soubor}{time.time()}".encode()).hexdigest()[:10]
                    new_filename = f"{base_name}-{hash_suffix}{extension}"
                    
                    target_path = os.path.join(target_dir, new_filename)
                    
                    # Zkopírovat soubor
                    shutil.copy2(best_file, target_path)
                    
                    # Získat metadata
                    width, height, file_size = get_image_dimensions_and_size(target_path)
                    mime_type = get_mime_type(target_path)
                    
                    # Vytvořit WordPress attachment
                    wp_url = f"{WP_SITE_URL}/wp-content/uploads/obrazek/{polozka_id}/{new_filename}"
                    relative_path = f"obrazek/{polozka_id}/{new_filename}"
                    
                    # Vložit do wp_posts
                    mysql_cursor.execute(f"""
                        INSERT INTO {TBL_WP_POSTS} 
                        (post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt, 
                         post_status, comment_status, ping_status, post_password, post_name, to_ping, 
                         pinged, post_modified, post_modified_gmt, post_content_filtered, post_parent, 
                         guid, menu_order, post_type, post_mime_type, comment_count)
                        VALUES 
                        (1, NOW(), NOW(), '', %s, '', 'inherit', 'open', 'closed', '', %s, '', '', 
                         NOW(), NOW(), '', 0, %s, 0, 'attachment', %s, 0)
                    """, (popisek or soubor, base_name, wp_url, mime_type))
                    
                    wp_id = mysql_cursor.lastrowid
                    
                    # Přidat metadata
                    metadata_entries = [
                        ('_wp_attached_file', relative_path),
                        ('sabre_polozka_id', polozka_id),
                        ('sabre_original_id', id_obrazek),
                        ('sabre_original_filename', soubor),
                        ('sabre_priority', priorita),
                        ('sabre_type', typ),
                        ('sabre_selected_quality', category)
                    ]
                    
                    if width and height:
                        import json
                        metadata_entries.append(('_wp_attachment_metadata', json.dumps({
                            'width': width,
                            'height': height,
                            'file': relative_path,
                            'filesize': file_size
                        })))
                    
                    for meta_key, meta_value in metadata_entries:
                        mysql_cursor.execute(f"""
                            INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value)
                            VALUES (%s, %s, %s)
                        """, (wp_id, meta_key, meta_value))
                    
                    # Uložit do mapování (POUZE unikátní klíč!)
                    image_map[unique_key] = {
                        'wp_id': wp_id,
                        'wp_url': wp_url,
                        'original_filename': soubor,
                        'new_filename': new_filename,
                        'polozka_id': polozka_id,
                        'type': typ,
                        'priority': priorita,
                        'quality': category,
                        'dimensions': f"{width}x{height}" if width and height else None
                    }
                    
                    mysql_conn.commit()
                    stats['uploaded'] += 1
                    
                    logging.info(f"✅ Migrován: {unique_key} -> ID {wp_id} ({category})")
                    
                except Exception as e:
                    mysql_conn.rollback()
                    logging.error(f"❌ Chyba při migraci {unique_key}: {e}")
                    stats['failed'] += 1
            
            # Uložit mapování po každé dávce
            save_mapping(image_map, 'image_map.json')
            
            # Posunout offset
            offset += batch_size
            
            # Logovat progress
            logging.info(f"📊 Progress: {stats['uploaded']} nahráno, {stats['failed']} selhalo, "
                        f"{stats['originals_selected']} originálů, {stats['thumbnails_selected']} náhledů")
    
    except Exception as e:
        logging.error(f"Kritická chyba v migrate_images_fixed: {e}")
    
    finally:
        save_mapping(image_map, 'image_map.json')
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        
        # Finální statistiky
        logging.info("🎉 OPRAVENÁ MIGRACE OBRÁZKŮ DOKONČENA")
        logging.info("=" * 60)
        logging.info(f"📊 STATISTIKY:")
        logging.info(f"   Zpracováno: {stats['processed']}")
        logging.info(f"   Úspěšně nahráno: {stats['uploaded']}")
        logging.info(f"   Přeskočeno (existující): {stats['skipped_existing']}")
        logging.info(f"   Selhalo: {stats['failed']}")
        logging.info(f"   Originály vybrány: {stats['originals_selected']}")
        logging.info(f"   Náhledy vybrány: {stats['thumbnails_selected']}")
        logging.info(f"   Kvalita originálů: {stats['originals_selected']/(stats['originals_selected']+stats['thumbnails_selected'])*100:.1f}%")

if __name__ == "__main__":
    migrate_images_fixed()
