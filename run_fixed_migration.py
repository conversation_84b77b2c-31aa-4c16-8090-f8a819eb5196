#!/usr/bin/env python3
"""
Hlavní skript pro spuštění opravené migrace
Řeší všechny identifikované problémy původní migrace
"""
import os
import sys
import time
import logging
from datetime import datetime

# Import opravených migrač<PERSON><PERSON><PERSON>k<PERSON>
from migrate_images_mujdum_fixed import migrate_images_fixed
from migrate_articles_mujdum_fixed import migrate_articles_fixed
from migrate_galleries_mujdum import migrate_galleries  # Použijeme existující
from reset_migration_database import main as reset_database

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_prerequisites():
    """Zkontroluje předpoklady pro migraci"""
    print("🔍 KONTROLA PŘEDPOKLADŮ")
    print("=" * 40)
    
    checks = []
    
    # 1. Kontrola databázových připojení
    try:
        from db_connectors import get_pg_connection, get_mysql_connection
        
        # PostgreSQL
        pg_conn = get_pg_connection()
        pg_cursor = pg_conn.cursor()
        pg_cursor.execute("SELECT COUNT(*) FROM prefix_clanek")
        pg_articles = pg_cursor.fetchone()[0]
        pg_cursor.close()
        pg_conn.close()
        
        checks.append(f"✅ PostgreSQL: {pg_articles} článků")
        
        # MySQL
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor()
        mysql_cursor.execute("SELECT COUNT(*) FROM mujdum_posts WHERE post_type = 'post'")
        wp_posts = mysql_cursor.fetchone()[0]
        mysql_cursor.close()
        mysql_conn.close()
        
        checks.append(f"✅ MySQL: {wp_posts} existujících postů")
        
    except Exception as e:
        checks.append(f"❌ Databáze: {e}")
        return False, checks
    
    # 2. Kontrola adresářů
    from config_mujdum import ORIGINAL_IMAGES_PATH, WP_UPLOADS_PATH
    
    if os.path.exists(ORIGINAL_IMAGES_PATH):
        # Spočítat obrázky
        image_count = 0
        for root, dirs, files in os.walk(ORIGINAL_IMAGES_PATH):
            image_count += len([f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif'))])
        checks.append(f"✅ Původní obrázky: {image_count} souborů")
    else:
        checks.append(f"❌ Původní obrázky: {ORIGINAL_IMAGES_PATH} neexistuje")
        return False, checks
    
    if os.path.exists(WP_UPLOADS_PATH):
        checks.append(f"✅ WordPress uploads: {WP_UPLOADS_PATH}")
    else:
        checks.append(f"⚠️  WordPress uploads: {WP_UPLOADS_PATH} neexistuje (bude vytvořen)")
    
    # 3. Kontrola mapovacích souborů
    mapping_dir = "mappings"
    if os.path.exists(mapping_dir):
        checks.append(f"✅ Mapování: {mapping_dir}")
    else:
        os.makedirs(mapping_dir, exist_ok=True)
        checks.append(f"✅ Mapování: {mapping_dir} (vytvořen)")
    
    # 4. Kontrola volného místa
    try:
        statvfs = os.statvfs('.')
        free_space_gb = (statvfs.f_frsize * statvfs.f_bavail) / (1024**3)
        if free_space_gb > 5:
            checks.append(f"✅ Volné místo: {free_space_gb:.1f} GB")
        else:
            checks.append(f"⚠️  Volné místo: {free_space_gb:.1f} GB (může být málo)")
    except:
        checks.append(f"⚠️  Volné místo: nelze zjistit")
    
    # Vypsat výsledky
    for check in checks:
        print(f"   {check}")
    
    # Určit úspěch
    failed_checks = [c for c in checks if c.startswith('❌')]
    success = len(failed_checks) == 0
    
    if success:
        print(f"\n✅ Všechny předpoklady splněny")
    else:
        print(f"\n❌ {len(failed_checks)} kontrol selhalo")
    
    return success, checks

def run_migration_phase(phase_name, migration_function, *args, **kwargs):
    """Spustí jednu fázi migrace s error handlingem"""
    print(f"\n🚀 FÁZE: {phase_name}")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        result = migration_function(*args, **kwargs)
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ {phase_name} dokončena za {duration/60:.1f} minut")
        return True, result
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"❌ {phase_name} selhala po {duration/60:.1f} minutách: {e}")
        logging.error(f"Chyba v {phase_name}: {e}", exc_info=True)
        return False, None

def create_migration_report(results):
    """Vytvoří zprávu o migraci"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"migration_report_{timestamp}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("ZPRÁVA O OPRAVENÉ MIGRACI\n")
        f.write("=" * 50 + "\n")
        f.write(f"Datum: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for phase, (success, result) in results.items():
            f.write(f"{phase}: {'✅ ÚSPĚCH' if success else '❌ SELHALO'}\n")
            if result:
                f.write(f"   Výsledek: {result}\n")
            f.write("\n")
        
        # Celkové hodnocení
        successful_phases = sum(1 for success, _ in results.values() if success)
        total_phases = len(results)
        
        f.write(f"SOUHRN:\n")
        f.write(f"Úspěšné fáze: {successful_phases}/{total_phases}\n")
        f.write(f"Celková úspěšnost: {successful_phases/total_phases*100:.1f}%\n")
        
        if successful_phases == total_phases:
            f.write("\n🎉 MIGRACE BYLA ÚSPĚŠNÁ!\n")
        else:
            f.write(f"\n⚠️  MIGRACE BYLA ČÁSTEČNĚ ÚSPĚŠNÁ\n")
            f.write(f"Zkontrolujte selhané fáze a opravte problémy.\n")
    
    print(f"📄 Zpráva uložena: {report_file}")
    return report_file

def main():
    """Hlavní funkce pro opravenou migraci"""
    print("🔧 OPRAVENÁ MIGRACE MUJDUM-2")
    print("=" * 60)
    print("🎯 Řeší všechny identifikované problémy původní migrace:")
    print("   ✅ Preferuje originály před náhledy")
    print("   ✅ Eliminuje cross-article contamination")
    print("   ✅ Správná logika featured images")
    print("   ✅ Přesná konverze URL")
    print()
    
    # 1. Kontrola předpokladů
    success, checks = check_prerequisites()
    if not success:
        print("❌ Předpoklady nesplněny - migrace přerušena")
        return
    
    # 2. Volba režimu
    print("\n📋 REŽIMY MIGRACE:")
    print("1. Kompletní reset + nová migrace (doporučeno)")
    print("2. Pouze nová migrace (bez resetu)")
    print("3. Pouze reset databáze")
    
    choice = input("\nVyberte režim (1-3): ").strip()
    
    results = {}
    
    if choice == "1":
        # Kompletní reset + migrace
        print("\n⚠️  VAROVÁNÍ: Kompletní reset smaže všechna současná data!")
        confirm = input("Pokračovat? (ano/ne): ").lower().strip()
        if confirm not in ['ano', 'yes', 'y']:
            print("❌ Migrace zrušena")
            return
        
        # Reset
        success, result = run_migration_phase("Reset databáze", reset_database)
        results["Reset"] = (success, result)
        
        if not success:
            print("❌ Reset selhal - migrace přerušena")
            return
        
        # Migrace obrázků
        success, result = run_migration_phase("Migrace obrázků", migrate_images_fixed)
        results["Obrázky"] = (success, result)
        
        if not success:
            print("⚠️  Migrace obrázků selhala - pokračuji v článcích")
        
        # Migrace článků
        success, result = run_migration_phase("Migrace článků", migrate_articles_fixed)
        results["Články"] = (success, result)
        
        # Migrace galerií
        success, result = run_migration_phase("Migrace galerií", migrate_galleries)
        results["Galerie"] = (success, result)
        
    elif choice == "2":
        # Pouze migrace
        print("\n🔄 Spouštím migraci bez resetu...")
        
        success, result = run_migration_phase("Migrace obrázků", migrate_images_fixed)
        results["Obrázky"] = (success, result)
        
        success, result = run_migration_phase("Migrace článků", migrate_articles_fixed)
        results["Články"] = (success, result)
        
        success, result = run_migration_phase("Migrace galerií", migrate_galleries)
        results["Galerie"] = (success, result)
        
    elif choice == "3":
        # Pouze reset
        success, result = run_migration_phase("Reset databáze", reset_database)
        results["Reset"] = (success, result)
        
    else:
        print("❌ Neplatná volba")
        return
    
    # 3. Vytvoření zprávy
    report_file = create_migration_report(results)
    
    # 4. Finální souhrn
    successful_phases = sum(1 for success, _ in results.values() if success)
    total_phases = len(results)
    
    print(f"\n" + "=" * 60)
    print(f"📊 FINÁLNÍ SOUHRN")
    print(f"=" * 60)
    print(f"Úspěšné fáze: {successful_phases}/{total_phases}")
    print(f"Celková úspěšnost: {successful_phases/total_phases*100:.1f}%")
    
    if successful_phases == total_phases:
        print(f"\n🎉 MIGRACE BYLA ÚSPĚŠNÁ!")
        print(f"🌐 Zkontrolujte web: http://mujdum.test")
        print(f"🔍 Testujte článek: http://mujdum.test/?p=425714")
    else:
        print(f"\n⚠️  MIGRACE BYLA ČÁSTEČNĚ ÚSPĚŠNÁ")
        print(f"📄 Zkontrolujte zprávu: {report_file}")
    
    print(f"\n📝 Doporučené další kroky:")
    print(f"   1. Zkontrolujte kvalitu obrázků")
    print(f"   2. Ověřte featured images")
    print(f"   3. Otestujte galerie")
    print(f"   4. Zkontrolujte URL v obsahu článků")

if __name__ == "__main__":
    main()
