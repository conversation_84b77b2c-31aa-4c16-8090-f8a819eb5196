#!/usr/bin/env python3
"""
Cílená oprava konkrétního <PERSON>ánku - demonstrace <PERSON><PERSON><PERSON>: "Nemáte zahradu? Prostor pro relaxaci nabídne terasa, balkon nebo střecha"
"""
import re
import os
from db_connectors import get_mysql_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA

def analyze_article_images(post_id, correct_polozka_id):
    """Analyzuje dostupné obrázky pro článek"""
    print(f"🔍 ANALÝZA OBRÁZKŮ PRO ČLÁNEK {post_id}")
    print("=" * 60)
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Najít všechny obrázky pro tento článek
        mysql_cursor.execute(f"""
            SELECT p.ID, p.guid, pm.meta_value as polozka_id
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = 'sabre_polozka_id'
            AND pm.meta_value = %s
            ORDER BY p.ID
        """, (correct_polozka_id,))
        
        images = mysql_cursor.fetchall()
        
        print(f"📊 Nalezeno {len(images)} obrázků pro polozka_id: {correct_polozka_id}")
        print()
        
        # Kategorizovat obrázky podle velikosti
        originals = []
        large_images = []
        medium_images = []
        small_thumbnails = []
        
        for img_id, img_url, polozka_id in images:
            filename = os.path.basename(img_url)
            
            # Extrahovat rozměry z názvu souboru
            size_match = re.search(r'_(\d+)x(\d+)', filename)
            
            if size_match:
                width, height = map(int, size_match.groups())
                
                if width <= 150 or height <= 150:
                    small_thumbnails.append((img_id, img_url, width, height))
                elif width <= 400 or height <= 400:
                    medium_images.append((img_id, img_url, width, height))
                else:
                    large_images.append((img_id, img_url, width, height))
            else:
                # Bez rozměrů = originál
                originals.append((img_id, img_url, None, None))
        
        print(f"📏 KATEGORIZACE PODLE VELIKOSTI:")
        print(f"   🎯 Originály: {len(originals)}")
        for img_id, img_url, _, _ in originals:
            print(f"      ID {img_id}: {os.path.basename(img_url)}")
        
        print(f"   📐 Velké obrázky (>400px): {len(large_images)}")
        for img_id, img_url, w, h in large_images:
            print(f"      ID {img_id}: {os.path.basename(img_url)} ({w}x{h})")
        
        print(f"   📏 Střední obrázky (150-400px): {len(medium_images)}")
        for img_id, img_url, w, h in medium_images[:3]:  # Jen první 3
            print(f"      ID {img_id}: {os.path.basename(img_url)} ({w}x{h})")
        if len(medium_images) > 3:
            print(f"      ... a dalších {len(medium_images) - 3}")
        
        print(f"   🔍 Malé náhledy (<150px): {len(small_thumbnails)}")
        for img_id, img_url, w, h in small_thumbnails[:3]:  # Jen první 3
            print(f"      ID {img_id}: {os.path.basename(img_url)} ({w}x{h})")
        if len(small_thumbnails) > 3:
            print(f"      ... a dalších {len(small_thumbnails) - 3}")
        
        mysql_cursor.close()
        mysql_conn.close()
        
        return {
            'originals': originals,
            'large': large_images,
            'medium': medium_images,
            'small': small_thumbnails
        }
        
    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")
        mysql_cursor.close()
        mysql_conn.close()
        return None

def fix_featured_image(post_id, correct_polozka_id, image_categories):
    """Opraví featured image pro článek"""
    print(f"\n🔧 OPRAVA FEATURED IMAGE")
    print("=" * 40)
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Zkontrolovat současný featured image
        mysql_cursor.execute(f"""
            SELECT pm.meta_value as thumb_id, p.guid, pm2.meta_value as polozka_id
            FROM {TBL_WP_POSTMETA} pm
            LEFT JOIN {TBL_WP_POSTS} p ON pm.meta_value = p.ID
            LEFT JOIN {TBL_WP_POSTMETA} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = 'sabre_polozka_id'
            WHERE pm.post_id = %s AND pm.meta_key = '_thumbnail_id'
        """, (post_id,))
        
        current_featured = mysql_cursor.fetchone()
        
        if current_featured:
            current_id, current_url, current_polozka = current_featured
            print(f"📸 Současný featured image:")
            print(f"   ID: {current_id}")
            print(f"   URL: {current_url}")
            print(f"   Polozka ID: {current_polozka}")
            
            if current_polozka != correct_polozka_id:
                print(f"   ⚠️  PROBLÉM: Patří do jiného článku!")
            else:
                print(f"   ✅ Patří do správného článku")
                
                # Zkontrolovat velikost
                filename = os.path.basename(current_url)
                size_match = re.search(r'_(\d+)x(\d+)', filename)
                if size_match:
                    width, height = map(int, size_match.groups())
                    if width <= 200 or height <= 200:
                        print(f"   ⚠️  PROBLÉM: Příliš malý ({width}x{height})")
                    else:
                        print(f"   ✅ Dobrá velikost ({width}x{height})")
                        mysql_cursor.close()
                        mysql_conn.close()
                        return True  # Není třeba opravovat
        else:
            print(f"❌ Featured image není nastaven")
        
        # Najít nejlepší kandidát na featured image
        print(f"\n🎯 HLEDÁNÍ NEJLEPŠÍHO KANDIDÁTA:")
        
        best_candidate = None
        
        # 1. Preferovat originály
        if image_categories['originals']:
            best_candidate = image_categories['originals'][0]
            print(f"   ✅ Vybrán originál: ID {best_candidate[0]}")
        
        # 2. Pak velké obrázky
        elif image_categories['large']:
            # Seřadit podle velikosti (největší první)
            sorted_large = sorted(image_categories['large'], key=lambda x: x[2] * x[3], reverse=True)
            best_candidate = sorted_large[0]
            print(f"   ✅ Vybrán velký obrázek: ID {best_candidate[0]} ({best_candidate[2]}x{best_candidate[3]})")
        
        # 3. Pak střední obrázky
        elif image_categories['medium']:
            sorted_medium = sorted(image_categories['medium'], key=lambda x: x[2] * x[3], reverse=True)
            best_candidate = sorted_medium[0]
            print(f"   ⚠️  Vybrán střední obrázek: ID {best_candidate[0]} ({best_candidate[2]}x{best_candidate[3]})")
        
        # 4. V nouzi i malé náhledy
        elif image_categories['small']:
            sorted_small = sorted(image_categories['small'], key=lambda x: x[2] * x[3], reverse=True)
            best_candidate = sorted_small[0]
            print(f"   ⚠️  Vybrán malý náhled: ID {best_candidate[0]} ({best_candidate[2]}x{best_candidate[3]})")
        
        if not best_candidate:
            print(f"   ❌ Žádný vhodný kandidát nenalezen!")
            mysql_cursor.close()
            mysql_conn.close()
            return False
        
        # Nastavit nový featured image
        new_featured_id = best_candidate[0]
        
        print(f"\n💾 NASTAVOVÁNÍ NOVÉHO FEATURED IMAGE:")
        print(f"   Nové ID: {new_featured_id}")
        print(f"   URL: {best_candidate[1]}")
        
        # Zkontrolovat, zda už featured image existuje
        if current_featured:
            # Aktualizovat existující
            mysql_cursor.execute(f"""
                UPDATE {TBL_WP_POSTMETA}
                SET meta_value = %s
                WHERE post_id = %s AND meta_key = '_thumbnail_id'
            """, (new_featured_id, post_id))
            print(f"   ✅ Featured image aktualizován")
        else:
            # Vytvořit nový záznam
            mysql_cursor.execute(f"""
                INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value)
                VALUES (%s, '_thumbnail_id', %s)
            """, (post_id, new_featured_id))
            print(f"   ✅ Featured image nastaven")
        
        mysql_conn.commit()
        mysql_cursor.close()
        mysql_conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při opravě featured image: {e}")
        mysql_conn.rollback()
        mysql_cursor.close()
        mysql_conn.close()
        return False

def fix_gallery_images(post_id, correct_polozka_id, image_categories):
    """Opraví obrázky v galerii"""
    print(f"\n🔧 OPRAVA GALERIÍ")
    print("=" * 40)
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Získat obsah článku
        mysql_cursor.execute(f"""
            SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s
        """, (post_id,))
        
        content_result = mysql_cursor.fetchone()
        if not content_result:
            print(f"❌ Obsah článku nenalezen")
            mysql_cursor.close()
            mysql_conn.close()
            return False
        
        content = content_result[0]
        
        # Najít gallery shortcodes
        gallery_pattern = r'\[gallery([^\]]*ids="([^"]+)"[^\]]*)\]'
        matches = list(re.finditer(gallery_pattern, content))
        
        if not matches:
            print(f"ℹ️  Žádné galerie nenalezeny")
            mysql_cursor.close()
            mysql_conn.close()
            return True
        
        print(f"📊 Nalezeno {len(matches)} galerií")
        
        updated_content = content
        total_replaced = 0
        
        for i, match in enumerate(matches):
            print(f"\n🖼️  Galerie {i+1}:")
            
            full_shortcode = match.group(0)
            ids_string = match.group(2)
            current_ids = [id.strip() for id in ids_string.split(',') if id.strip().isdigit()]
            
            print(f"   Současné ID: {', '.join(current_ids)}")
            
            # Zkontrolovat každý obrázek v galerii
            new_ids = []
            replaced_count = 0
            
            for img_id in current_ids:
                # Zkontrolovat polozka_id tohoto obrázku
                mysql_cursor.execute(f"""
                    SELECT pm.meta_value as polozka_id, p.guid
                    FROM {TBL_WP_POSTMETA} pm
                    JOIN {TBL_WP_POSTS} p ON pm.post_id = p.ID
                    WHERE pm.post_id = %s AND pm.meta_key = 'sabre_polozka_id'
                """, (img_id,))
                
                img_info = mysql_cursor.fetchone()
                
                if img_info:
                    img_polozka, img_url = img_info
                    
                    if img_polozka == correct_polozka_id:
                        # Obrázek patří do správného článku
                        filename = os.path.basename(img_url)
                        size_match = re.search(r'_(\d+)x(\d+)', filename)
                        
                        if size_match:
                            width, height = map(int, size_match.groups())
                            
                            # Pokud je malý náhled, zkusit najít větší verzi
                            if width <= 200 or height <= 200:
                                print(f"      ID {img_id}: malý náhled ({width}x{height}) - hledám větší")
                                
                                # Najít větší verzi stejného obrázku
                                base_name = re.sub(r'_\d+x\d+', '', filename)
                                base_name = re.sub(r'-[a-f0-9]{10,}', '', base_name)
                                
                                # Hledat větší verze
                                better_found = False
                                for category in ['originals', 'large', 'medium']:
                                    for candidate in image_categories[category]:
                                        candidate_filename = os.path.basename(candidate[1])
                                        candidate_base = re.sub(r'_\d+x\d+', '', candidate_filename)
                                        candidate_base = re.sub(r'-[a-f0-9]{10,}', '', candidate_base)
                                        
                                        if candidate_base == base_name:
                                            new_ids.append(str(candidate[0]))
                                            replaced_count += 1
                                            print(f"         → nahrazeno ID {candidate[0]}")
                                            better_found = True
                                            break
                                    if better_found:
                                        break
                                
                                if not better_found:
                                    new_ids.append(img_id)
                                    print(f"         → ponecháno (lepší verze nenalezena)")
                            else:
                                new_ids.append(img_id)
                                print(f"      ID {img_id}: OK ({width}x{height})")
                        else:
                            new_ids.append(img_id)
                            print(f"      ID {img_id}: originál - OK")
                    else:
                        print(f"      ID {img_id}: ⚠️  z jiného článku ({img_polozka}) - odstraňujem")
                        # Nepoužít tento obrázek
                else:
                    print(f"      ID {img_id}: ❓ bez polozka_id - ponechávám")
                    new_ids.append(img_id)
            
            # Aktualizovat shortcode pokud byly změny
            if replaced_count > 0:
                new_shortcode = full_shortcode.replace(ids_string, ','.join(new_ids))
                updated_content = updated_content.replace(full_shortcode, new_shortcode)
                total_replaced += replaced_count
                print(f"   ✅ Nahrazeno {replaced_count} obrázků")
            else:
                print(f"   ℹ️  Žádné změny")
        
        # Uložit aktualizovaný obsah
        if total_replaced > 0:
            mysql_cursor.execute(f"""
                UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
            """, (updated_content, post_id))
            mysql_conn.commit()
            print(f"\n✅ Obsah článku aktualizován - celkem {total_replaced} nahrazení")
        else:
            print(f"\nℹ️  Žádné změny v galeriích")
        
        mysql_cursor.close()
        mysql_conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při opravě galerií: {e}")
        mysql_conn.rollback()
        mysql_cursor.close()
        mysql_conn.close()
        return False

def main():
    """Hlavní funkce pro opravu konkrétního článku"""
    print("🔧 CÍLENÁ OPRAVA KONKRÉTNÍHO ČLÁNKU")
    print("=" * 60)
    
    # Parametry konkrétního článku
    post_id = 425714
    correct_polozka_id = "67ff6c71b1da7"
    article_title = "Nemáte zahradu? Prostor pro relaxaci nabídne terasa, balkon nebo střecha"
    
    print(f"📖 Článek: {article_title}")
    print(f"🆔 Post ID: {post_id}")
    print(f"🔑 Polozka ID: {correct_polozka_id}")
    print()
    
    # 1. Analyzovat dostupné obrázky
    image_categories = analyze_article_images(post_id, correct_polozka_id)
    
    if not image_categories:
        print("❌ Nepodařilo se analyzovat obrázky")
        return
    
    # 2. Opravit featured image
    featured_success = fix_featured_image(post_id, correct_polozka_id, image_categories)
    
    # 3. Opravit galerie
    gallery_success = fix_gallery_images(post_id, correct_polozka_id, image_categories)
    
    # 4. Souhrn
    print(f"\n" + "=" * 60)
    print(f"📋 SOUHRN OPRAV")
    print(f"=" * 60)
    print(f"Featured image: {'✅ Opraveno' if featured_success else '❌ Chyba'}")
    print(f"Galerie: {'✅ Opraveno' if gallery_success else '❌ Chyba'}")
    
    if featured_success and gallery_success:
        print(f"\n🎉 ČLÁNEK ÚSPĚŠNĚ OPRAVEN!")
        print(f"🌐 Zkontrolujte na: http://mujdum.test/?p={post_id}")
    else:
        print(f"\n⚠️  Některé opravy selhaly - zkontrolujte logy výše")

if __name__ == "__main__":
    main()
