#!/usr/bin/env python3
"""
Finální opravný skript pro všechny problémy s migrac<PERSON> MUJDUM-2
Opravuje: galerie s malými náhledy, chybějící obrázky v obsahu, featured images
"""
import os
import re
import logging
import json
from bs4 import BeautifulSoup
from db_connectors import get_mysql_connection, get_pg_connection
from config_mujdum import (
    TBL_WP_POSTS, TBL_WP_POSTMETA, TBL_CLANEK, TBL_OBRAZEK,
    WP_UPLOADS_PATH, WP_SITE_URL
)
from utils_mujdum import load_mapping, save_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ImageSizeHelper:
    """Helper pro práci s velikostmi obrázků"""
    
    @staticmethod
    def extract_dimensions(filename):
        """Extrahuje rozměry z názvu souboru"""
        match = re.search(r'_(\d+)x(\d+)', filename)
        if match:
            return int(match.group(1)), int(match.group(2))
        return None, None
    
    @staticmethod
    def is_small_thumbnail(filename):
        """Zjistí zda je soubor malý náhled"""
        width, height = ImageSizeHelper.extract_dimensions(filename)
        if width and height:
            return width <= 150 or height <= 150
        return False
    
    @staticmethod
    def is_large_image(filename):
        """Zjistí zda je soubor velký obrázek"""
        width, height = ImageSizeHelper.extract_dimensions(filename)
        if width and height:
            return width > 400 and height > 300
        return True  # Soubory bez rozměrů považujeme za velké (originály)
    
    @staticmethod
    def is_medium_image(filename):
        """Zjistí zda je soubor střední obrázek (vhodný pro featured)"""
        width, height = ImageSizeHelper.extract_dimensions(filename)
        if width and height:
            return 150 < width <= 600 and 150 < height <= 600
        return False
    
    @staticmethod
    def get_base_filename(filename):
        """Získá základní název souboru bez rozměrů a hash"""
        # Odstranit rozměry: file_800x600.jpg -> file.jpg
        base = re.sub(r'_\d+x\d+', '', filename)
        # Odstranit hash: file-abc123def.jpg -> file.jpg  
        base = re.sub(r'-[a-f0-9]{10,}', '', base)
        return base

def find_better_image_version(image_id, image_map, prefer_large=True):
    """
    Najde lepší verzi obrázku (větší nebo menší podle potřeby)
    """
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Získat současný obrázek
        mysql_cursor.execute(f"""
            SELECT guid FROM {TBL_WP_POSTS} WHERE ID = %s AND post_type = 'attachment'
        """, (image_id,))
        
        result = mysql_cursor.fetchone()
        if not result:
            return None
            
        current_url = result[0]
        current_filename = os.path.basename(current_url)
        base_name = ImageSizeHelper.get_base_filename(current_filename)
        
        # Najít všechny verze tohoto obrázku v mapování
        candidates = []
        
        for path, info in image_map.items():
            if isinstance(info, dict) and 'wp_id' in info and 'wp_url' in info:
                candidate_filename = os.path.basename(info['wp_url'])
                candidate_base = ImageSizeHelper.get_base_filename(candidate_filename)
                
                if candidate_base == base_name:
                    width, height = ImageSizeHelper.extract_dimensions(candidate_filename)
                    candidates.append({
                        'wp_id': info['wp_id'],
                        'wp_url': info['wp_url'],
                        'filename': candidate_filename,
                        'width': width or 9999,
                        'height': height or 9999,
                        'is_large': ImageSizeHelper.is_large_image(candidate_filename),
                        'is_medium': ImageSizeHelper.is_medium_image(candidate_filename),
                        'is_small': ImageSizeHelper.is_small_thumbnail(candidate_filename)
                    })
        
        if not candidates:
            return None
            
        # Vybrat nejlepší kandidát
        if prefer_large:
            # Pro galerie - preferovat velké obrázky
            large_candidates = [c for c in candidates if c['is_large']]
            if large_candidates:
                # Seřadit podle velikosti (největší první)
                large_candidates.sort(key=lambda x: x['width'] * x['height'], reverse=True)
                return large_candidates[0]
        else:
            # Pro featured images - preferovat střední velikosti
            medium_candidates = [c for c in candidates if c['is_medium']]
            if medium_candidates:
                # Seřadit podle velikosti (největší první)
                medium_candidates.sort(key=lambda x: x['width'] * x['height'], reverse=True)
                return medium_candidates[0]
            
            # Fallback na velké pokud nejsou střední
            large_candidates = [c for c in candidates if c['is_large']]
            if large_candidates:
                large_candidates.sort(key=lambda x: x['width'] * x['height'])  # Nejmenší z velkých
                return large_candidates[0]
        
        # Fallback na jakýkoliv jiný než malý
        non_small = [c for c in candidates if not c['is_small']]
        if non_small:
            non_small.sort(key=lambda x: x['width'] * x['height'], reverse=True)
            return non_small[0]
            
        return None
        
    finally:
        mysql_cursor.close()
        mysql_conn.close()

def fix_galleries():
    """Opraví galerie - nahradí malé náhledy za velké obrázky"""
    
    logging.info("🔧 Spouštím opravu galerií...")
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    image_map = load_mapping('image_map.json')
    
    fixed_galleries = 0
    total_replaced = 0
    
    try:
        # Najít všechny články s galeriemi
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, p.post_content
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post' 
            AND pm.meta_key = 'sabre_database_version' 
            AND pm.meta_value = 'mujdum-2'
            AND p.post_content LIKE '%[gallery%'
        """)
        
        articles = mysql_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles)} článků s galeriemi")
        
        for post_id, title, content in articles:
            logging.info(f"Zpracovávám článek: {title}")
            
            # Najít gallery shortcodes
            gallery_pattern = r'\[gallery([^\]]*ids="([^"]+)"[^\]]*)\]'
            matches = list(re.finditer(gallery_pattern, content))
            
            if not matches:
                continue
                
            updated_content = content
            article_replaced = 0
            
            for match in matches:
                full_shortcode = match.group(0)
                ids_string = match.group(2)
                current_ids = [id.strip() for id in ids_string.split(',')]
                
                new_ids = []
                for img_id in current_ids:
                    if not img_id.isdigit():
                        new_ids.append(img_id)
                        continue
                        
                    # Najít lepší verzi obrázku
                    better_version = find_better_image_version(img_id, image_map, prefer_large=True)
                    
                    if better_version and better_version['wp_id'] != int(img_id):
                        new_ids.append(str(better_version['wp_id']))
                        article_replaced += 1
                        logging.debug(f"Nahrazen obrázek ID {img_id} -> {better_version['wp_id']}")
                    else:
                        new_ids.append(img_id)
                
                # Aktualizovat shortcode
                if article_replaced > 0:
                    new_shortcode = full_shortcode.replace(ids_string, ','.join(new_ids))
                    updated_content = updated_content.replace(full_shortcode, new_shortcode)
            
            # Uložit aktualizovaný obsah
            if article_replaced > 0:
                mysql_cursor.execute(f"""
                    UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
                """, (updated_content, post_id))
                
                fixed_galleries += 1
                total_replaced += article_replaced
                logging.info(f"Článek '{title}': nahrazeno {article_replaced} obrázků v galerii")
        
        mysql_conn.commit()
        logging.info(f"✅ Oprava galerií dokončena: {fixed_galleries} článků, {total_replaced} obrázků nahrazeno")
        
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Chyba při opravě galerií: {e}")
    finally:
        mysql_cursor.close()
        mysql_conn.close()

def fix_featured_images():
    """Opraví featured images - nastaví lepší verze"""
    
    logging.info("🔧 Spouštím opravu featured images...")
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    image_map = load_mapping('image_map.json')
    
    fixed_featured = 0
    
    try:
        # Najít články bez featured image nebo s malými náhledy
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, pm.meta_value as current_thumb_id
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm_version ON p.ID = pm_version.post_id
            LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
            WHERE p.post_type = 'post' 
            AND pm_version.meta_key = 'sabre_database_version' 
            AND pm_version.meta_value = 'mujdum-2'
        """)
        
        articles = mysql_cursor.fetchall()
        logging.info(f"Kontroluji {len(articles)} článků pro featured images")
        
        for post_id, title, current_thumb_id in articles:
            better_thumb_id = None
            
            if current_thumb_id:
                # Zkusit najít lepší verzi současného featured image
                better_version = find_better_image_version(current_thumb_id, image_map, prefer_large=False)
                if better_version and better_version['wp_id'] != int(current_thumb_id):
                    if ImageSizeHelper.is_medium_image(better_version['filename']):
                        better_thumb_id = better_version['wp_id']
            else:
                # Nemá featured image - zkusit najít z galerie
                mysql_cursor.execute(f"""
                    SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s
                """, (post_id,))
                
                content_result = mysql_cursor.fetchone()
                if content_result:
                    content = content_result[0]
                    gallery_matches = re.findall(r'\[gallery[^\]]*ids="([^"]+)"[^\]]*\]', content)
                    
                    if gallery_matches:
                        ids = gallery_matches[0].split(',')
                        for img_id in ids[:3]:  # Zkusit první 3 obrázky z galerie
                            img_id = img_id.strip()
                            if img_id.isdigit():
                                candidate = find_better_image_version(img_id, image_map, prefer_large=False)
                                if candidate and ImageSizeHelper.is_medium_image(candidate['filename']):
                                    better_thumb_id = candidate['wp_id']
                                    break
            
            # Nastavit nebo aktualizovat featured image
            if better_thumb_id:
                if current_thumb_id:
                    mysql_cursor.execute(f"""
                        UPDATE {TBL_WP_POSTMETA} 
                        SET meta_value = %s 
                        WHERE post_id = %s AND meta_key = '_thumbnail_id'
                    """, (better_thumb_id, post_id))
                else:
                    mysql_cursor.execute(f"""
                        INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value)
                        VALUES (%s, '_thumbnail_id', %s)
                    """, (post_id, better_thumb_id))
                
                fixed_featured += 1
                action = "aktualizován" if current_thumb_id else "nastaven"
                logging.info(f"Featured image {action} pro článek '{title}': ID {better_thumb_id}")
        
        mysql_conn.commit()
        logging.info(f"✅ Oprava featured images dokončena: {fixed_featured} článků")
        
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Chyba při opravě featured images: {e}")
    finally:
        mysql_cursor.close()
        mysql_conn.close()

def fix_content_images():
    """Opraví obrázky v obsahu článků"""
    
    logging.info("🔧 Spouštím opravu obrázků v obsahu...")
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    image_map = load_mapping('image_map.json')
    
    fixed_articles = 0
    total_fixed_images = 0
    
    try:
        # Najít články s obrázky v obsahu
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, p.post_content
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post' 
            AND pm.meta_key = 'sabre_database_version' 
            AND pm.meta_value = 'mujdum-2'
            AND p.post_content LIKE '%<img%'
        """)
        
        articles = mysql_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles)} článků s obrázky v obsahu")
        
        for post_id, title, content in articles:
            try:
                soup = BeautifulSoup(content, 'html.parser')
                images = soup.find_all('img')
                
                if not images:
                    continue
                
                article_fixed_count = 0
                
                for img in images:
                    if img.has_attr('src'):
                        src = img['src']
                        original_src = src
                        
                        # Zkusit opravit různé typy problematických URL
                        fixed_url = None
                        
                        # 1. Rozklíčovat původní URL z mujdum.cz
                        if 'mujdum.cz/obrazek/' in src:
                            match = re.search(r'/obrazek/([^/]+/[^/\?]+)', src)
                            if match:
                                path_part = match.group(1)
                                # Najít v mapování
                                for old_path, new_info in image_map.items():
                                    if isinstance(new_info, dict) and 'wp_url' in new_info:
                                        if old_path == path_part or os.path.basename(old_path) == os.path.basename(path_part):
                                            # Preferovat velké obrázky pro obsah
                                            if ImageSizeHelper.is_large_image(new_info['wp_url']) or not ImageSizeHelper.is_small_thumbnail(new_info['wp_url']):
                                                fixed_url = new_info['wp_url']
                                                break
                        
                        # 2. Zkusit najít podle názvu souboru
                        if not fixed_url:
                            filename = os.path.basename(src.split('?')[0])
                            base_name = ImageSizeHelper.get_base_filename(filename)
                            
                            # Najít velkou verzi tohoto obrázku
                            best_candidate = None
                            best_size = 0
                            
                            for old_path, new_info in image_map.items():
                                if isinstance(new_info, dict) and 'wp_url' in new_info:
                                    candidate_name = os.path.basename(new_info['wp_url'])
                                    candidate_base = ImageSizeHelper.get_base_filename(candidate_name)
                                    
                                    if candidate_base == base_name:
                                        if ImageSizeHelper.is_large_image(candidate_name):
                                            width, height = ImageSizeHelper.extract_dimensions(candidate_name)
                                            if width and height:
                                                size = width * height
                                                if size > best_size:
                                                    best_size = size
                                                    best_candidate = new_info['wp_url']
                                            elif not best_candidate:  # Originál bez rozměrů
                                                best_candidate = new_info['wp_url']
                            
                            if best_candidate:
                                fixed_url = best_candidate
                        
                        # Aktualizovat URL pokud bylo nalezeno lepší
                        if fixed_url and fixed_url != src:
                            img['src'] = fixed_url
                            article_fixed_count += 1
                            logging.debug(f"Opraven obrázek: {original_src} -> {fixed_url}")
                
                # Uložit aktualizovaný obsah
                if article_fixed_count > 0:
                    updated_content = str(soup)
                    mysql_cursor.execute(f"""
                        UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
                    """, (updated_content, post_id))
                    
                    fixed_articles += 1
                    total_fixed_images += article_fixed_count
                    logging.info(f"Článek '{title}': opraveno {article_fixed_count} obrázků v obsahu")
                    
            except Exception as e:
                logging.error(f"Chyba při zpracování článku '{title}': {e}")
                continue
        
        mysql_conn.commit()
        logging.info(f"✅ Oprava obrázků v obsahu dokončena: {fixed_articles} článků, {total_fixed_images} obrázků")
        
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Chyba při opravě obrázků v obsahu: {e}")
    finally:
        mysql_cursor.close()
        mysql_conn.close()

def main():
    """Hlavní funkce pro spuštění všech oprav"""
    
    print("=" * 70)
    print("🔧 FINÁLNÍ OPRAVA VŠECH PROBLÉMŮ S OBRÁZKY - MUJDUM-2")
    print("=" * 70)
    
    logging.info("Spouštím kompletní opravu migrace...")
    
    # 1. Opravit galerie (nahradit malé náhledy)
    fix_galleries()
    
    # 2. Opravit featured images
    fix_featured_images()
    
    # 3. Opravit obrázky v obsahu článků
    fix_content_images()
    
    print("\n" + "=" * 70)
    print("✅ VŠECHNY OPRAVY DOKONČENY!")
    print("=" * 70)
    print("🎯 Doporučuji nyní zkontrolovat několik článků na webu")
    print("🎯 Zejména ty zmíněné: 'Rádce na beton', 'Skladování vína', etc.")

if __name__ == "__main__":
    main()
