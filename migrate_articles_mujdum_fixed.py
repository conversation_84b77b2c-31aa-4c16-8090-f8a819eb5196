#!/usr/bin/env python3
"""
OPRAVENÁ MIGRACE ČLÁNKŮ - Řeší problémy s featured images a URL konverzí
- Správná logika výběru featured images podle priority
- Přesná konverze URL pouze pro obrázky ze stejného článku
- Eliminace cross-article contamination
"""
import logging
import mysql.connector
import re
import os
from bs4 import BeautifulSoup
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import (
    TBL_CLANEK, TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA,
    TBL_WP_TERM_RELATIONSHIPS, DEFAULT_WP_USER_ID
)
from utils_mujdum import (
    load_mapping, save_mapping, generate_slug,
    format_wp_datetime, format_wp_datetime_gmt,
    extract_author_name
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def find_best_featured_image(unikatni_id, image_map, pg_cursor):
    """
    Najde nejlepší featured image podle priority z původní databáze
    
    Args:
        unikatni_id: ID článku
        image_map: Mapování obrázků
        pg_cursor: PostgreSQL cursor
    
    Returns:
        WordPress ID nejlepšího featured image nebo None
    """
    if not unikatni_id:
        return None
    
    try:
        # Najít všechny obrázky typu 0 (featured) seřazené podle priority
        pg_cursor.execute(f"""
            SELECT soubor, priorita, popisek
            FROM {TBL_OBRAZEK}
            WHERE polozka_id = %s AND active_state = 1 AND typ = 0
            ORDER BY priorita DESC, id_obrazek ASC
        """, (unikatni_id,))
        
        featured_candidates = pg_cursor.fetchall()
        
        if not featured_candidates:
            # Fallback: najít nejlepší obrázek typu 1 (galerie) s nejvyšší prioritou
            pg_cursor.execute(f"""
                SELECT soubor, priorita, popisek
                FROM {TBL_OBRAZEK}
                WHERE polozka_id = %s AND active_state = 1 AND typ = 1
                ORDER BY priorita DESC, id_obrazek ASC
                LIMIT 3
            """, (unikatni_id,))
            
            featured_candidates = pg_cursor.fetchall()
        
        if not featured_candidates:
            logging.warning(f"Žádné obrázky nenalezeny pro článek {unikatni_id}")
            return None
        
        # Projít kandidáty podle priority a najít v mapování
        for soubor, priorita, popisek in featured_candidates:
            # Hledat v mapování - preferovat přesnou shodu s polozka_id
            possible_keys = [
                f"{unikatni_id}/{soubor}",  # Přesná shoda
                soubor  # Fallback (ale riskantní kvůli cross-contamination)
            ]
            
            for key in possible_keys:
                if key in image_map and isinstance(image_map[key], dict):
                    wp_id = image_map[key].get('wp_id')
                    if wp_id:
                        # Ověřit, že obrázek skutečně patří do tohoto článku
                        if key.startswith(f"{unikatni_id}/"):
                            logging.info(f"Nalezen featured image: {soubor} (priorita {priorita}) -> ID {wp_id}")
                            return wp_id
                        else:
                            # Cross-contamination check
                            logging.warning(f"Potenciální cross-contamination pro {soubor} - přeskakuji")
                            continue
        
        logging.warning(f"Featured image nebyl nalezen v mapování pro článek {unikatni_id}")
        return None
        
    except Exception as e:
        logging.error(f"Chyba při hledání featured image pro {unikatni_id}: {e}")
        return None

def update_image_urls_precise(content, image_map, article_polozka_id):
    """
    Přesná aktualizace URL obrázků - pouze pro obrázky ze stejného článku
    
    Args:
        content: Obsah článku
        image_map: Mapování obrázků
        article_polozka_id: ID článku pro kontrolu
    
    Returns:
        Aktualizovaný obsah
    """
    if not content or not image_map or not article_polozka_id:
        return content
    
    updated_count = 0
    
    try:
        soup = BeautifulSoup(content, 'html.parser')
        images = soup.find_all('img')
        
        for img in images:
            for attr in ['src', 'data-src']:
                if img.has_attr(attr):
                    src = img[attr]
                    original_src = src
                    
                    # Extrahovat cestu z URL
                    if 'mujdum.cz/obrazek/' in src or '/obrazek/' in src:
                        match = re.search(r'/obrazek/([^/]+/[^/\?]+)', src)
                        if match:
                            path_part = match.group(1)  # např. "polozka_id/soubor.jpg"
                            
                            # Kontrola, že obrázek patří do tohoto článku
                            if path_part.startswith(f"{article_polozka_id}/"):
                                # Hledat v mapování
                                if path_part in image_map and isinstance(image_map[path_part], dict):
                                    new_url = image_map[path_part].get('wp_url')
                                    if new_url:
                                        img[attr] = new_url
                                        updated_count += 1
                                        logging.debug(f"URL aktualizováno: {original_src} -> {new_url}")
                                        continue
                            else:
                                # Obrázek z jiného článku - neaktualizovat
                                logging.debug(f"Cross-article obrázek ignorován: {path_part}")
                                continue
                    
                    # Fallback: hledat podle názvu souboru (pouze v rámci článku)
                    src_filename = os.path.basename(src.split('?')[0])
                    article_key = f"{article_polozka_id}/{src_filename}"
                    
                    if article_key in image_map and isinstance(image_map[article_key], dict):
                        new_url = image_map[article_key].get('wp_url')
                        if new_url:
                            img[attr] = new_url
                            updated_count += 1
                            logging.debug(f"URL aktualizováno (fallback): {original_src} -> {new_url}")
        
        content = str(soup)
        
    except Exception as e:
        logging.error(f"Chyba při aktualizaci URL: {e}")
    
    if updated_count > 0:
        logging.info(f"Aktualizováno {updated_count} URL obrázků v obsahu článku {article_polozka_id}")
    
    return content

def migrate_articles_fixed(batch_size=50, start_offset=0):
    """Opravená migrace článků"""
    logging.info("🔧 Spouštím OPRAVENOU migraci článků...")
    
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()

    # Načíst mapování
    category_map = load_mapping('category_map.json')
    user_map = load_mapping('user_map.json')
    image_map = load_mapping('image_map.json')
    article_map = load_mapping('article_map.json')

    # Statistiky
    stats = {
        'processed': 0,
        'migrated': 0,
        'skipped': 0,
        'failed': 0,
        'featured_images_set': 0,
        'featured_images_failed': 0,
        'url_updates': 0
    }

    try:
        # Získat celkový počet článků
        pg_cursor.execute(f"SELECT COUNT(*) FROM {TBL_CLANEK}")
        total_articles = pg_cursor.fetchone()[0]
        logging.info(f"Celkový počet článků: {total_articles}")

        # Zpracovat po dávkách
        offset = start_offset
        
        while True:
            pg_cursor.execute(f"""
                SELECT id_clanek, unikatni_id, rubrika_id, nazev, nahled, text, 
                       zobrazovat_od, aktualizovano, cas_vlozeni, autor, obrazek_src, 
                       obrazek_alt, active_state, cislo_casopisu, nazev_galerie
                FROM {TBL_CLANEK}
                ORDER BY id_clanek
                LIMIT {batch_size} OFFSET {offset}
            """)
            
            articles = pg_cursor.fetchall()
            if not articles:
                break
            
            logging.info(f"Zpracovávám dávku {offset}-{offset + len(articles)} z {total_articles}")
            
            for article in articles:
                (
                    id_clanek, unikatni_id, rubrika_id, nazev, nahled, text,
                    zobrazovat_od, aktualizovano, cas_vlozeni, autor, obrazek_src,
                    obrazek_alt, active_state, cislo_casopisu, nazev_galerie
                ) = article
                
                stats['processed'] += 1
                
                # Přeskočit už zpracované
                if str(id_clanek) in article_map:
                    stats['skipped'] += 1
                    continue
                
                try:
                    # Připravit data
                    post_title = nazev or f"Článek {id_clanek}"
                    post_name = generate_slug(nazev)
                    post_content = text or ""
                    
                    # OPRAVENÁ URL konverze - pouze pro obrázky z tohoto článku
                    if unikatni_id:
                        post_content = update_image_urls_precise(post_content, image_map, unikatni_id)
                        if post_content != (text or ""):
                            stats['url_updates'] += 1
                    
                    post_excerpt = nahled or ""
                    if post_excerpt:
                        soup = BeautifulSoup(post_excerpt, 'html.parser')
                        post_excerpt = soup.get_text()
                    
                    # Datumy
                    post_date = format_wp_datetime(zobrazovat_od or cas_vlozeni)
                    post_date_gmt = format_wp_datetime_gmt(zobrazovat_od or cas_vlozeni)
                    post_modified = format_wp_datetime(aktualizovano or cas_vlozeni)
                    post_modified_gmt = format_wp_datetime_gmt(aktualizovano or cas_vlozeni)
                    
                    # Autor
                    post_author = DEFAULT_WP_USER_ID
                    if autor and autor in user_map:
                        post_author = user_map[autor]
                    
                    # Status
                    post_status = 'publish' if active_state == 1 else 'draft'
                    
                    # Vložit článek
                    sql_posts = f"""
                        INSERT INTO {TBL_WP_POSTS}
                        (post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt,
                        post_status, comment_status, ping_status, post_name, post_modified, post_modified_gmt,
                        post_parent, guid, menu_order, post_type, post_mime_type, comment_count,
                        to_ping, pinged, post_content_filtered)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    mysql_cursor.execute(sql_posts, (
                        post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt,
                        post_status, 'open', 'open', post_name, post_modified, post_modified_gmt,
                        0, f"http://mujdum.test/?p=", 0, 'post', '', 0, '', '', ''
                    ))
                    wp_post_id = mysql_cursor.lastrowid
                    
                    # Aktualizovat GUID
                    mysql_cursor.execute(f"UPDATE {TBL_WP_POSTS} SET guid = %s WHERE ID = %s", 
                                        (f"http://mujdum.test/?p={wp_post_id}", wp_post_id))
                    
                    # Přiřadit kategorii
                    if rubrika_id and str(rubrika_id) in category_map:
                        wp_term_id = category_map[str(rubrika_id)]
                        mysql_cursor.execute(f"""
                            INSERT INTO {TBL_WP_TERM_RELATIONSHIPS}
                            (object_id, term_taxonomy_id, term_order)
                            VALUES (%s, %s, 0)
                        """, (wp_post_id, wp_term_id))
                    
                    # OPRAVENÉ nastavení featured image
                    featured_attachment_id = find_best_featured_image(unikatni_id, image_map, pg_cursor)
                    
                    if featured_attachment_id:
                        mysql_cursor.execute(f"""
                            INSERT INTO {TBL_WP_POSTMETA}
                            (post_id, meta_key, meta_value)
                            VALUES (%s, %s, %s)
                        """, (wp_post_id, '_thumbnail_id', featured_attachment_id))
                        stats['featured_images_set'] += 1
                        logging.info(f"Featured image nastaven: článek {wp_post_id} -> obrázek {featured_attachment_id}")
                    else:
                        stats['featured_images_failed'] += 1
                        logging.warning(f"Featured image nenalezen pro článek {id_clanek}")
                    
                    # Uložit metadata
                    metadata_entries = [
                        ('sabre_id', id_clanek),
                        ('sabre_database_version', 'mujdum-2')
                    ]
                    
                    if unikatni_id:
                        metadata_entries.append(('sabre_unikatni_id', unikatni_id))
                    if cislo_casopisu:
                        metadata_entries.append(('sabre_cislo_casopisu', cislo_casopisu))
                    if nazev_galerie:
                        metadata_entries.append(('sabre_nazev_galerie', nazev_galerie))
                    
                    for meta_key, meta_value in metadata_entries:
                        mysql_cursor.execute(f"""
                            INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value)
                            VALUES (%s, %s, %s)
                        """, (wp_post_id, meta_key, meta_value))
                    
                    mysql_conn.commit()
                    
                    # Aktualizovat mapování
                    article_map[str(id_clanek)] = wp_post_id
                    stats['migrated'] += 1
                    
                    logging.info(f"✅ Článek {id_clanek} migrován -> ID {wp_post_id}")
                    
                except Exception as e:
                    mysql_conn.rollback()
                    logging.error(f"❌ Chyba při migraci článku {id_clanek}: {e}")
                    stats['failed'] += 1
            
            # Uložit mapování po dávce
            save_mapping(article_map, 'article_map.json')
            offset += batch_size
            
            # Progress log
            logging.info(f"📊 Progress: {stats['migrated']} migrováno, {stats['failed']} selhalo")

    except Exception as e:
        logging.error(f"Kritická chyba: {e}")
    
    finally:
        save_mapping(article_map, 'article_map.json')
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        
        # Finální statistiky
        logging.info("🎉 OPRAVENÁ MIGRACE ČLÁNKŮ DOKONČENA")
        logging.info("=" * 60)
        logging.info(f"📊 STATISTIKY:")
        logging.info(f"   Zpracováno: {stats['processed']}")
        logging.info(f"   Migrováno: {stats['migrated']}")
        logging.info(f"   Přeskočeno: {stats['skipped']}")
        logging.info(f"   Selhalo: {stats['failed']}")
        logging.info(f"   Featured images nastaveny: {stats['featured_images_set']}")
        logging.info(f"   Featured images selhaly: {stats['featured_images_failed']}")
        logging.info(f"   Články s URL aktualizacemi: {stats['url_updates']}")
        
        success_rate = stats['migrated'] / (stats['migrated'] + stats['failed']) * 100 if (stats['migrated'] + stats['failed']) > 0 else 0
        featured_rate = stats['featured_images_set'] / stats['migrated'] * 100 if stats['migrated'] > 0 else 0
        
        logging.info(f"   Úspěšnost migrace: {success_rate:.1f}%")
        logging.info(f"   Úspěšnost featured images: {featured_rate:.1f}%")

if __name__ == "__main__":
    migrate_articles_fixed()
