#!/usr/bin/env python3
"""
Komplexní analýza migrace - porovnání původní PostgreSQL DB vs WordPress
Zaměření na článek: "Nemáte zahradu? Prostor pro relaxaci nabídne terasa, balkon nebo střecha"
"""
import json
import re
import os
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import TBL_CLANEK, TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA
from utils_mujdum import load_mapping

def analyze_original_postgresql_data(polozka_id):
    """Analyzuje původní data v PostgreSQL databázi"""
    print("=" * 80)
    print("🔍 ANALÝZA PŮVODNÍ DATABÁZE (PostgreSQL)")
    print("=" * 80)
    
    pg_conn = get_pg_connection()
    pg_cursor = pg_conn.cursor()
    
    try:
        # 1. Naj<PERSON>t článek v původní databázi
        pg_cursor.execute(f"""
            SELECT id_clanek, unikatni_id, nazev, obrazek_src, obrazek_alt, text
            FROM {TBL_CLANEK}
            WHERE unikatni_id = %s
        """, (polozka_id,))
        
        article = pg_cursor.fetchone()
        
        if not article:
            print(f"❌ Článek s polozka_id '{polozka_id}' nebyl nalezen v původní databázi!")
            pg_cursor.close()
            pg_conn.close()
            return None
        
        article_id, unique_id, title, featured_src, featured_alt, content = article
        
        print(f"📖 PŮVODNÍ ČLÁNEK:")
        print(f"   ID: {article_id}")
        print(f"   Název: {title}")
        print(f"   Unikátní ID: {unique_id}")
        print(f"   Featured image src: {featured_src}")
        print(f"   Featured image alt: {featured_alt}")
        print(f"   Délka obsahu: {len(content) if content else 0} znaků")
        
        # 2. Najít všechny obrázky pro tento článek
        print(f"\n📊 PŮVODNÍ OBRÁZKY (polozka_id: {unique_id}):")
        
        pg_cursor.execute(f"""
            SELECT id_obrazek, soubor, popisek, priorita, typ, active_state
            FROM {TBL_OBRAZEK}
            WHERE polozka_id = %s
            ORDER BY typ ASC, priorita DESC, id_obrazek ASC
        """, (unique_id,))
        
        images = pg_cursor.fetchall()
        
        print(f"   Celkem nalezeno: {len(images)} obrázků")
        print()
        
        original_data = {
            'article': {
                'id': article_id,
                'unique_id': unique_id,
                'title': title,
                'featured_src': featured_src,
                'featured_alt': featured_alt,
                'content': content
            },
            'images': {
                'featured': [],
                'gallery': [],
                'other': []
            }
        }
        
        for img_id, filename, desc, priority, img_type, active in images:
            status = "✅ AKTIVNÍ" if active == 1 else "❌ NEAKTIVNÍ"
            type_name = "FEATURED" if img_type == 0 else "GALERIE" if img_type == 1 else f"TYP_{img_type}"
            
            print(f"   🖼️  ID: {img_id} | {type_name} | Priorita: {priority} | {status}")
            print(f"      📁 Soubor: {filename}")
            print(f"      📝 Popis: {desc}")
            print()

            img_data = {
                'id': img_id,
                'filename': filename,
                'description': desc,
                'priority': priority,
                'type': img_type,
                'active': active
            }
            
            if img_type == 0:
                original_data['images']['featured'].append(img_data)
            elif img_type == 1:
                original_data['images']['gallery'].append(img_data)
            else:
                original_data['images']['other'].append(img_data)
        
        # 3. Analyzovat obsah článku na obrázky
        if content:
            print(f"🔍 ANALÝZA OBSAHU PŮVODNÍHO ČLÁNKU:")
            
            # Najít odkazy na obrázky v obsahu
            image_patterns = [
                r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>',
                r'obrazek/([^/]+/[^"\'\s]+)',
                r'mujdum\.cz/obrazek/([^"\'\s]+)'
            ]
            
            content_images = set()
            for pattern in image_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                content_images.update(matches)
            
            print(f"   Nalezeno {len(content_images)} odkazů na obrázky v obsahu:")
            for img_url in list(content_images)[:10]:  # Ukázat prvních 10
                print(f"      🔗 {img_url}")
            
            original_data['content_images'] = list(content_images)
        
        pg_cursor.close()
        pg_conn.close()
        
        return original_data
        
    except Exception as e:
        print(f"❌ Chyba při analýze původní databáze: {e}")
        pg_cursor.close()
        pg_conn.close()
        return None

def analyze_current_wordpress_state(polozka_id):
    """Analyzuje současný stav ve WordPress"""
    print("\n" + "=" * 80)
    print("🔍 ANALÝZA SOUČASNÉHO STAVU (WordPress)")
    print("=" * 80)
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # 1. Najít článek ve WordPress
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, p.post_content, p.post_status
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post'
            AND pm.meta_key = 'sabre_unikatni_id'
            AND pm.meta_value = %s
        """, (polozka_id,))
        
        wp_article = mysql_cursor.fetchone()
        
        if not wp_article:
            print(f"❌ Článek s polozka_id '{polozka_id}' nebyl nalezen ve WordPress!")
            mysql_cursor.close()
            mysql_conn.close()
            return None
        
        wp_post_id, wp_title, wp_content, wp_status = wp_article
        
        print(f"📖 WORDPRESS ČLÁNEK:")
        print(f"   Post ID: {wp_post_id}")
        print(f"   Název: {wp_title}")
        print(f"   Status: {wp_status}")
        print(f"   Délka obsahu: {len(wp_content) if wp_content else 0} znaků")
        
        # 2. Najít všechny obrázky pro tento článek ve WordPress
        print(f"\n📊 WORDPRESS OBRÁZKY (polozka_id: {polozka_id}):")
        
        mysql_cursor.execute(f"""
            SELECT p.ID, p.guid, p.post_title, pm1.meta_value as polozka_id, 
                   pm2.meta_value as attached_file, pm3.meta_value as metadata
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = 'sabre_polozka_id'
            LEFT JOIN {TBL_WP_POSTMETA} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_wp_attached_file'
            LEFT JOIN {TBL_WP_POSTMETA} pm3 ON p.ID = pm3.post_id AND pm3.meta_key = '_wp_attachment_metadata'
            WHERE p.post_type = 'attachment'
            AND pm1.meta_value = %s
            ORDER BY p.ID
        """, (polozka_id,))
        
        wp_images = mysql_cursor.fetchall()
        
        print(f"   Celkem nalezeno: {len(wp_images)} obrázků")
        print()
        
        wp_data = {
            'article': {
                'post_id': wp_post_id,
                'title': wp_title,
                'content': wp_content,
                'status': wp_status
            },
            'images': []
        }
        
        for wp_id, wp_url, wp_title_img, wp_polozka_id, attached_file, metadata in wp_images:
            filename = os.path.basename(wp_url)
            
            # Extrahovat rozměry z názvu souboru
            size_match = re.search(r'_(\d+)x(\d+)', filename)
            width = int(size_match.group(1)) if size_match else None
            height = int(size_match.group(2)) if size_match else None
            
            # Kategorizovat podle velikosti
            if size_match:
                if width <= 150 or height <= 150:
                    category = "MALÝ NÁHLED"
                elif width <= 400 or height <= 400:
                    category = "STŘEDNÍ"
                else:
                    category = "VELKÝ"
            else:
                category = "ORIGINÁL"
            
            print(f"   🖼️  ID: {wp_id} | {category}")
            print(f"      📁 Soubor: {filename}")
            print(f"      🔗 URL: {wp_url}")
            print(f"      📏 Rozměry: {width}x{height}" if width and height else "      📏 Rozměry: originál")
            print()
            
            wp_data['images'].append({
                'id': wp_id,
                'url': wp_url,
                'filename': filename,
                'title': wp_title_img,
                'polozka_id': wp_polozka_id,
                'attached_file': attached_file,
                'width': width,
                'height': height,
                'category': category
            })
        
        # 3. Zkontrolovat featured image
        mysql_cursor.execute(f"""
            SELECT pm.meta_value as thumbnail_id
            FROM {TBL_WP_POSTMETA} pm
            WHERE pm.post_id = %s AND pm.meta_key = '_thumbnail_id'
        """, (wp_post_id,))
        
        featured_result = mysql_cursor.fetchone()
        wp_data['featured_image_id'] = featured_result[0] if featured_result else None
        
        # 4. Analyzovat galerie v obsahu
        if wp_content:
            gallery_matches = re.findall(r'\[gallery[^\]]*ids="([^"]+)"[^\]]*\]', wp_content)
            wp_data['gallery_ids'] = []
            for ids_string in gallery_matches:
                ids = [id.strip() for id in ids_string.split(',') if id.strip().isdigit()]
                wp_data['gallery_ids'].extend(ids)
        
        mysql_cursor.close()
        mysql_conn.close()
        
        return wp_data
        
    except Exception as e:
        print(f"❌ Chyba při analýze WordPress: {e}")
        mysql_cursor.close()
        mysql_conn.close()
        return None

def analyze_image_mapping_issues(polozka_id, original_data, wp_data):
    """Analyzuje problémy v mapování obrázků"""
    print("\n" + "=" * 80)
    print("🔍 ANALÝZA MAPOVÁNÍ OBRÁZKŮ")
    print("=" * 80)
    
    try:
        image_map = load_mapping('image_map.json')
        
        # Najít všechny záznamy pro tento článek v mapování
        article_mappings = {}
        
        for path, info in image_map.items():
            if isinstance(info, dict) and polozka_id in path:
                article_mappings[path] = info
        
        print(f"📊 MAPOVÁNÍ PRO ČLÁNEK (polozka_id: {polozka_id}):")
        print(f"   Nalezeno {len(article_mappings)} záznamů v image_map.json")
        print()
        
        # Analyzovat každý záznam
        mapping_analysis = {
            'originals_in_map': [],
            'thumbnails_in_map': [],
            'missing_from_wp': [],
            'size_downgrades': []
        }
        
        for path, info in article_mappings.items():
            filename = os.path.basename(info.get('wp_url', ''))
            wp_id = info.get('wp_id')
            
            # Zkontrolovat, zda existuje ve WordPress
            wp_image = next((img for img in wp_data['images'] if img['id'] == wp_id), None)
            
            if not wp_image:
                mapping_analysis['missing_from_wp'].append({
                    'path': path,
                    'wp_id': wp_id,
                    'filename': filename
                })
                continue
            
            # Analyzovat velikost
            size_match = re.search(r'_(\d+)x(\d+)', filename)
            
            if size_match:
                width, height = map(int, size_match.groups())
                mapping_analysis['thumbnails_in_map'].append({
                    'path': path,
                    'wp_id': wp_id,
                    'filename': filename,
                    'size': (width, height),
                    'wp_image': wp_image
                })
            else:
                mapping_analysis['originals_in_map'].append({
                    'path': path,
                    'wp_id': wp_id,
                    'filename': filename,
                    'wp_image': wp_image
                })
        
        print(f"📈 ANALÝZA MAPOVÁNÍ:")
        print(f"   Originály v mapování: {len(mapping_analysis['originals_in_map'])}")
        print(f"   Náhledy v mapování: {len(mapping_analysis['thumbnails_in_map'])}")
        print(f"   Chybí ve WordPress: {len(mapping_analysis['missing_from_wp'])}")
        print()
        
        # Ukázat originály
        if mapping_analysis['originals_in_map']:
            print(f"🎯 ORIGINÁLY V MAPOVÁNÍ:")
            for item in mapping_analysis['originals_in_map']:
                print(f"   ✅ {item['filename']} (ID: {item['wp_id']})")
        
        # Ukázat náhledy
        if mapping_analysis['thumbnails_in_map']:
            print(f"\n📏 NÁHLEDY V MAPOVÁNÍ:")
            for item in mapping_analysis['thumbnails_in_map'][:5]:  # Jen prvních 5
                width, height = item['size']
                print(f"   📐 {item['filename']} ({width}x{height}) (ID: {item['wp_id']})")
        
        # Ukázat chybějící
        if mapping_analysis['missing_from_wp']:
            print(f"\n❌ CHYBÍ VE WORDPRESS:")
            for item in mapping_analysis['missing_from_wp']:
                print(f"   ❌ {item['filename']} (ID: {item['wp_id']}) - cesta: {item['path']}")
        
        return mapping_analysis
        
    except Exception as e:
        print(f"❌ Chyba při analýze mapování: {e}")
        return None

def compare_original_vs_current(original_data, wp_data, mapping_analysis):
    """Porovná původní data s aktuálním stavem"""
    print("\n" + "=" * 80)
    print("📊 POROVNÁNÍ PŮVODNÍ DB vs WORDPRESS")
    print("=" * 80)
    
    if not original_data or not wp_data:
        print("❌ Chybí data pro porovnání")
        return
    
    print(f"📖 ČLÁNEK:")
    print(f"   Původní název: {original_data['article']['title']}")
    print(f"   WordPress název: {wp_data['article']['title']}")
    print(f"   Shoda názvů: {'✅' if original_data['article']['title'] == wp_data['article']['title'] else '❌'}")
    
    print(f"\n🖼️  OBRÁZKY:")
    orig_total = len(original_data['images']['featured']) + len(original_data['images']['gallery']) + len(original_data['images']['other'])
    wp_total = len(wp_data['images'])
    
    print(f"   Původní databáze: {orig_total} obrázků")
    print(f"   WordPress: {wp_total} obrázků")
    print(f"   Rozdíl: {wp_total - orig_total}")
    
    print(f"\n📏 KVALITA OBRÁZKŮ:")
    
    # Analyzovat kvalitu featured images
    orig_featured = original_data['images']['featured']
    if orig_featured:
        best_orig_featured = orig_featured[0]  # Vezmi první featured image
        print(f"   Nejlepší původní featured: {best_orig_featured['filename']}")
        print(f"      Priorita: {best_orig_featured['priority']}")
        print(f"      Popis: {best_orig_featured['description']}")
    
    # Současný featured image
    if wp_data.get('featured_image_id'):
        current_featured = next((img for img in wp_data['images'] if img['id'] == int(wp_data['featured_image_id'])), None)
        if current_featured:
            print(f"   Současný WordPress featured: {current_featured['filename']}")
            if current_featured['width'] and current_featured['height']:
                print(f"      Rozměry: {current_featured['width']}x{current_featured['height']}")
                
                # Porovnat s původním featured
                if orig_featured:
                    orig_filename = best_orig_featured['filename']
                    current_filename = current_featured['filename']

                    if orig_filename in current_filename:
                        print(f"      ✅ Správný obrázek z původní DB")
                    else:
                        print(f"      ⚠️  PROBLÉM: Jiný obrázek než v původní DB!")
                        print(f"         Původní: {orig_filename}")
                        print(f"         Současný: {current_filename}")

                    # Kontrola velikosti
                    if current_featured['width'] and current_featured['height']:
                        if current_featured['width'] <= 200 or current_featured['height'] <= 200:
                            print(f"      ⚠️  PROBLÉM: Malý náhled místo kvalitního obrázku!")
                        else:
                            print(f"      ✅ Dobrá velikost")
            else:
                print(f"      Rozměry: originál")
    
    print(f"\n🔍 DOPORUČENÍ:")
    
    # Najít nejlepší dostupné obrázky
    wp_originals = [img for img in wp_data['images'] if img['category'] == 'ORIGINÁL']
    wp_large = [img for img in wp_data['images'] if img['category'] == 'VELKÝ']
    
    if wp_originals:
        print(f"   ✅ Dostupné originály: {len(wp_originals)}")
        print(f"      Doporučuji použít: {wp_originals[0]['filename']} (ID: {wp_originals[0]['id']})")
    elif wp_large:
        print(f"   📐 Dostupné velké obrázky: {len(wp_large)}")
        best_large = max(wp_large, key=lambda x: (x['width'] or 0) * (x['height'] or 0))
        print(f"      Doporučuji použít: {best_large['filename']} (ID: {best_large['id']})")
    else:
        print(f"   ⚠️  Pouze malé náhledy dostupné - možná problém v migraci")

def main():
    """Hlavní funkce pro komplexní analýzu"""
    polozka_id = "67ff6c71b1da7"
    
    print("🔍 KOMPLEXNÍ ANALÝZA MIGRACE")
    print(f"📖 Polozka ID: {polozka_id}")
    print()
    
    # 1. Analyzovat původní PostgreSQL data
    original_data = analyze_original_postgresql_data(polozka_id)
    
    # 2. Analyzovat současný WordPress stav
    wp_data = analyze_current_wordpress_state(polozka_id)
    
    # 3. Analyzovat mapování obrázků
    mapping_analysis = analyze_image_mapping_issues(polozka_id, original_data, wp_data)
    
    # 4. Porovnat a vyhodnotit
    compare_original_vs_current(original_data, wp_data, mapping_analysis)
    
    # 5. Uložit výsledky
    results = {
        'original_data': original_data,
        'wp_data': wp_data,
        'mapping_analysis': mapping_analysis
    }
    
    with open('migration_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n💾 Výsledky uloženy do migration_analysis_results.json")
    print(f"📝 Na základě této analýzy můžeme identifikovat a opravit základní problémy migrace")

if __name__ == "__main__":
    main()
