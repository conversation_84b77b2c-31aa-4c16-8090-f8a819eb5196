# Migrace MUJDUM-2 do WordPress

Tento dokument popisuje migraci pro databázi **MUJDUM-2**, kter<PERSON> má odlišnou strukturu od původní databáze "dumabyt". 

## 🔍 Klíčové rozdíly MUJDUM-2

### Databázová struktura
| Aspekt | Původní (dumabyt) | MUJDUM-2 | Status |
|--------|-------------------|----------|--------|
| **Galerie tabulky** | `prefix_galerie`, `prefix_galerie_polozka` | `prefix_fotogalerie` (prázdná) | ❌ **ZMĚNA** |
| **Článkové galerie** | Přes `prefix_galerie_polozka` | Přes `prefix_obrazek` s `polozka_id` | ❌ **ZMĚNA** |
| **Název galerie** | V `prefix_galerie.nazev` | V `prefix_clanek.nazev_galerie` | ❌ **ZMĚNA** |
| **Sloupce** | Obsahuje sloupec `zdroj` | Sloupec `zdroj` neexistuje | ❌ **ZMĚNA** |

### Datová statistika
- **Články**: 6 175 aktivních
- **Obrázky**: 55 600 aktivních  
- **Fotogalerie**: 0 (tabulka existuje, ale je prázdná)

## 🚀 Rychlý start

### 1. Jednoduchá migrace (doporučeno)
```bash
# Zkopíruj celou složku s obrázky do WordPress
sudo cp -r /old/path/obrazek/ /path/to/wordpress/wp-content/uploads/

# Spusť automatickou migraci
python run_migration_mujdum.py
```

### 2. Krok za krokem
```bash
python migrate_categories.py
python migrate_users.py
python migrate_images_mujdum_simple.py
python migrate_articles_mujdum.py
python migrate_galleries_mujdum.py
```

## 📁 MUJDUM-specifické soubory

### Konfigurační soubory
- **`config_mujdum.py`** - konfigurace pro databázi mujdum-2
  - Odstraněny odkazy na neexistující tabulky galerií
  - Přidána `TBL_FOTOGALERIE` (prázdná)
  - Komentáře o změnách v databázové struktuře

### Migrační skripty
- **`migrate_images_mujdum_simple.py`** - migrace obrázků bez kopírování souborů
- **`migrate_articles_mujdum.py`** - migrace článků s podporou `nazev_galerie`
- **`migrate_galleries_mujdum.py`** - migrace galerií z `prefix_obrazek`
- **`utils_mujdum.py`** - utility funkce pro mujdum-2
- **`run_migration_mujdum.py`** - automatický spouštěč
- **`reset_migration_mujdum.py`** - reset migrace

### Dokumentace
- **`INSTRUKCE_MUJDUM_SIMPLE.md`** - návod na jednoduchou migraci
- **`MIGRATION_MUJDUM_CHANGES.md`** - detailní přehled změn

## 🔧 Opravné skripty

### Univerzální opravy
- **`fix_final_mujdum_issues.py`** - finální oprava všech problémů s obrázky
  - Opravuje galerie (nahrazuje malé náhledy za velké obrázky)
  - Opravuje featured images
  - Opravuje obrázky v obsahu článků

- **`fix_all_content_images.py`** - oprava obrázků v obsahu všech článků

### Specifické opravy
- **`fix_sadrokarton_article.py`** - příklad opravy konkrétního článku

### Debug nástroje
- **`debug_original_db.py`** - analýza původní databáze
- **`debug_content.py`** - debug obsahu článků
- **`debug_paths.py`** - debug cest k obrázkům
- **`debug_specific_article.py`** - analýza konkrétního článku
- **`analyze_image_sizes.py`** - analýza velikostí obrázků

## 🔍 Diagnostika problémů

### Kontrola mapování
```bash
# Zkontroluj mapování obrázků
python debug_paths.py

# Analyzuj konkrétní článek
python debug_specific_article.py "Název článku"

# Zkontroluj velikosti obrázků
python analyze_image_sizes.py
```

### Oprava problémů
```bash
# Oprav všechny problémy s obrázky
python fix_final_mujdum_issues.py

# Oprav pouze obsahy článků
python fix_all_content_images.py

# Reset a nová migrace
python reset_migration_mujdum.py
python run_migration_mujdum.py
```

## 📊 Očekávané výsledky

Po úspěšné migraci:
- ✅ **6 175** migravených článků
- ✅ **55 600** migravených obrázků (databázové záznamy)
- ✅ **Správně fungující** featured images
- ✅ **Správně fungující** obrázky v obsahu
- ✅ **Správně fungující** galerie

## ⚠️ Specifika MUJDUM-2

### Galerie
1. **Neexistují samostatné galerie** - vše se řeší přes obrázky článků
2. **Typ obrázku** má význam:
   - `typ = 0`: Úvodní obrázek článku (featured image)
   - `typ = 1`: Obrázky pro galerii
3. **Název galerie** je uložen přímo v článku (`nazev_galerie`)

### Mapování obrázků
- Obrázky se mapují podle `polozka_id = unikatni_id` článku
- Pokud článek má pouze 1 obrázek → žádná galerie  
- Pokud má více obrázků → první jako featured, zbytek do galerie

### Cesty k obrázkům
- Původní: `/old/path/obrazek/soubor.jpg`
- WordPress: `/wp-content/uploads/obrazek/soubor.jpg`
- Pouze vytváří databázové záznamy, fyzické soubory se kopírují ručně

## 🛠️ Řešení častých problémů

### Problém: Obrázky se nezobrazují
```bash
# Zkontroluj práva
ls -la /path/to/wordpress/wp-content/uploads/obrazek/

# Zkontroluj, že složka existuje
find /wp-content/uploads/obrazek/ -name "*.jpg" | head -5
```

### Problém: Chybí galerie
```bash
# Spusť opravu galerií
python migrate_galleries_mujdum.py

# Nebo univerzální opravu
python fix_final_mujdum_issues.py
```

### Problém: Špatné cesty v obsahu
```bash
# Oprav cesty obrázků
python fix_all_content_images.py
```

## 🎯 Tipy pro úspěch

1. **Vždy nejprve zkopíruj obrázky** - migrace spoléhá na jejich existenci
2. **Zkontroluj cesty v .env** - ujisti se, že WP_UPLOADS_PATH je správný  
3. **Testuj na malém vzorku** - použij debug skripty
4. **Zálohuj databázi** - před spuštěním migrace
5. **Sleduj logy** - migrace vytváří detailní logy

## 📝 Vytvořené soubory

Po migraci najdeš v `mappings/`:
- `article_map.json` - mapování článků
- `category_map.json` - mapování kategorií  
- `user_map.json` - mapování uživatelů
- `image_map.json` - mapování obrázků
- `gallery_map.json` - mapování galerií

## 📞 Podpora

Pro řešení problémů použij debug nástroje:
```bash
# Obecná analýza
python debug_original_db.py

# Konkrétní článek
python debug_specific_article.py "Název článku"

# Analýza cest
python debug_paths.py
```

---

Tento přístup je **mnohem efektivnější** než původní migrace a je optimalizován pro strukturu databáze MUJDUM-2! 🚀
