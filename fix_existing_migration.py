#!/usr/bin/env python3
"""
OPRAVA EXISTUJÍCÍ MIGRACE - Neopí<PERSON><PERSON> soubory, jen opravuje mapování a databázi
- Analyzuje už nahrané obrázky v wp-content/uploads/obrazek/
- Opravuje image_map.json podle skutečně nahraných souborů
- Preferuje originály před n<PERSON>hledy
- Eliminuje cross-article contamination
- Opravuje featured images podle priority z původní DB
"""
import os
import re
import json
import logging
from PIL import Image
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import (
    TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA,
    WP_UPLOADS_PATH, WP_SITE_URL
)
from utils_mujdum import load_mapping, save_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def analyze_uploaded_images():
    """Analyzuje už nahrané obrázky v wp-content/uploads/obrazek/"""
    print("🔍 ANALÝZA NAHRANÝCH OBRÁZKŮ")
    print("=" * 50)
    
    obrazek_dir = os.path.join(WP_UPLOADS_PATH, "obrazek")
    
    if not os.path.exists(obrazek_dir):
        print(f"❌ Adresář {obrazek_dir} neexistuje!")
        return {}
    
    uploaded_images = {}
    total_files = 0
    
    # Projít všechny polozka_id adresáře
    for polozka_id in os.listdir(obrazek_dir):
        polozka_dir = os.path.join(obrazek_dir, polozka_id)
        
        if not os.path.isdir(polozka_dir):
            continue
        
        print(f"📁 Analyzuji: {polozka_id}")
        
        images_in_article = []
        
        for filename in os.listdir(polozka_dir):
            file_path = os.path.join(polozka_dir, filename)
            
            if not filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
                continue
            
            total_files += 1
            
            # Získat rozměry
            try:
                with Image.open(file_path) as img:
                    width, height = img.size
                    file_size = os.path.getsize(file_path)
            except Exception as e:
                logging.warning(f"Nelze načíst obrázek {file_path}: {e}")
                width = height = file_size = None
            
            # Kategorizovat podle velikosti
            if width and height:
                if width <= 150 or height <= 150:
                    category = 'small_thumbnail'
                elif width <= 400 or height <= 400:
                    category = 'medium'
                else:
                    category = 'large'
            else:
                # Podle názvu souboru
                if re.search(r'_\d+x\d+', filename):
                    category = 'thumbnail'
                else:
                    category = 'original'
            
            # Extrahovat původní název
            original_filename = re.sub(r'-[a-f0-9]{10,}', '', filename)
            
            images_in_article.append({
                'filename': filename,
                'original_filename': original_filename,
                'file_path': file_path,
                'width': width,
                'height': height,
                'file_size': file_size,
                'category': category,
                'wp_url': f"{WP_SITE_URL}/wp-content/uploads/obrazek/{polozka_id}/{filename}"
            })
        
        if images_in_article:
            uploaded_images[polozka_id] = images_in_article
            print(f"   Nalezeno {len(images_in_article)} obrázků")
    
    print(f"\n📊 SOUHRN:")
    print(f"   Polozka_id adresářů: {len(uploaded_images)}")
    print(f"   Celkem souborů: {total_files}")
    
    return uploaded_images

def get_wordpress_image_ids():
    """Získá ID všech obrázků z WordPress databáze"""
    print("\n🔍 NAČÍTÁNÍ WORDPRESS IMAGE ID")
    print("=" * 40)
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Najít všechny attachmenty s polozka_id
        mysql_cursor.execute(f"""
            SELECT p.ID, p.guid, pm.meta_value as polozka_id, pm2.meta_value as original_filename
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = 'sabre_polozka_id'
            LEFT JOIN {TBL_WP_POSTMETA} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = 'sabre_original_filename'
            WHERE p.post_type = 'attachment'
        """)
        
        wp_images = {}
        
        for wp_id, wp_url, polozka_id, original_filename in mysql_cursor.fetchall():
            filename = os.path.basename(wp_url)
            
            if polozka_id not in wp_images:
                wp_images[polozka_id] = {}
            
            wp_images[polozka_id][filename] = {
                'wp_id': wp_id,
                'wp_url': wp_url,
                'original_filename': original_filename
            }
        
        mysql_cursor.close()
        mysql_conn.close()
        
        total_images = sum(len(images) for images in wp_images.values())
        print(f"   Nalezeno {total_images} WordPress attachmentů")
        
        return wp_images
        
    except Exception as e:
        print(f"❌ Chyba při načítání WordPress ID: {e}")
        mysql_cursor.close()
        mysql_conn.close()
        return {}

def create_fixed_image_mapping(uploaded_images, wp_images):
    """Vytvoří opravené mapování obrázků"""
    print("\n🔧 VYTVÁŘENÍ OPRAVENÉHO MAPOVÁNÍ")
    print("=" * 50)
    
    fixed_mapping = {}
    stats = {
        'mapped': 0,
        'originals_preferred': 0,
        'thumbnails_used': 0,
        'missing_wp_id': 0,
        'cross_contamination_prevented': 0
    }
    
    for polozka_id, images in uploaded_images.items():
        print(f"📁 Zpracovávám: {polozka_id}")
        
        if polozka_id not in wp_images:
            print(f"   ⚠️  Žádné WordPress ID pro {polozka_id}")
            continue
        
        wp_images_for_article = wp_images[polozka_id]
        
        # Seskupit podle původního názvu
        grouped_by_original = {}
        for img in images:
            orig_name = img['original_filename']
            if orig_name not in grouped_by_original:
                grouped_by_original[orig_name] = []
            grouped_by_original[orig_name].append(img)
        
        # Pro každý původní název vybrat nejlepší verzi
        for original_name, versions in grouped_by_original.items():
            # Seřadit podle kvality (originály > velké > střední > malé)
            quality_order = {'original': 4, 'large': 3, 'medium': 2, 'small_thumbnail': 1, 'thumbnail': 1}
            versions.sort(key=lambda x: (
                quality_order.get(x['category'], 0),
                (x['width'] or 0) * (x['height'] or 0),
                x['file_size'] or 0
            ), reverse=True)
            
            best_version = versions[0]
            
            # Najít WordPress ID
            wp_info = wp_images_for_article.get(best_version['filename'])
            
            if wp_info:
                # Vytvořit mapování POUZE s polozka_id (žádné duplicity!)
                map_key = f"{polozka_id}/{original_name}"
                
                fixed_mapping[map_key] = {
                    'wp_id': wp_info['wp_id'],
                    'wp_url': best_version['wp_url'],
                    'original_filename': original_name,
                    'selected_filename': best_version['filename'],
                    'polozka_id': polozka_id,
                    'category': best_version['category'],
                    'dimensions': f"{best_version['width']}x{best_version['height']}" if best_version['width'] else None
                }
                
                stats['mapped'] += 1
                
                if best_version['category'] in ['original', 'large']:
                    stats['originals_preferred'] += 1
                else:
                    stats['thumbnails_used'] += 1
                
                print(f"   ✅ {original_name} -> {best_version['filename']} ({best_version['category']})")
                
            else:
                stats['missing_wp_id'] += 1
                print(f"   ❌ Chybí WordPress ID pro: {best_version['filename']}")
    
    print(f"\n📊 STATISTIKY MAPOVÁNÍ:")
    print(f"   Zmapováno: {stats['mapped']}")
    print(f"   Originály/velké: {stats['originals_preferred']}")
    print(f"   Náhledy: {stats['thumbnails_used']}")
    print(f"   Chybí WordPress ID: {stats['missing_wp_id']}")
    print(f"   Kvalita: {stats['originals_preferred']/stats['mapped']*100:.1f}% originálů/velkých")
    
    return fixed_mapping, stats

def fix_featured_images_from_original_db(fixed_mapping):
    """Opraví featured images podle priority z původní databáze"""
    print("\n🎯 OPRAVA FEATURED IMAGES")
    print("=" * 40)
    
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()
    
    stats = {
        'articles_processed': 0,
        'featured_fixed': 0,
        'featured_failed': 0
    }
    
    try:
        # Najít všechny články s unikatni_id
        mysql_cursor.execute(f"""
            SELECT p.ID, pm.meta_value as unikatni_id
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post' AND pm.meta_key = 'sabre_unikatni_id'
        """)
        
        articles = mysql_cursor.fetchall()
        print(f"📊 Nalezeno {len(articles)} článků")
        
        for wp_post_id, unikatni_id in articles:
            stats['articles_processed'] += 1
            
            # Najít nejlepší featured image z původní DB
            pg_cursor.execute(f"""
                SELECT soubor, priorita
                FROM {TBL_OBRAZEK}
                WHERE polozka_id = %s AND active_state = 1 AND typ = 0
                ORDER BY priorita DESC, id_obrazek ASC
                LIMIT 1
            """, (unikatni_id,))
            
            featured_result = pg_cursor.fetchone()
            
            if not featured_result:
                # Fallback: nejlepší z galerie
                pg_cursor.execute(f"""
                    SELECT soubor, priorita
                    FROM {TBL_OBRAZEK}
                    WHERE polozka_id = %s AND active_state = 1 AND typ = 1
                    ORDER BY priorita DESC, id_obrazek ASC
                    LIMIT 1
                """, (unikatni_id,))
                featured_result = pg_cursor.fetchone()
            
            if featured_result:
                featured_filename, priority = featured_result
                map_key = f"{unikatni_id}/{featured_filename}"
                
                if map_key in fixed_mapping:
                    new_featured_id = fixed_mapping[map_key]['wp_id']
                    
                    # Aktualizovat nebo vytvořit featured image
                    mysql_cursor.execute(f"""
                        INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value)
                        VALUES (%s, '_thumbnail_id', %s)
                        ON DUPLICATE KEY UPDATE meta_value = %s
                    """, (wp_post_id, new_featured_id, new_featured_id))
                    
                    stats['featured_fixed'] += 1
                    print(f"   ✅ Článek {wp_post_id}: featured -> {featured_filename} (ID {new_featured_id})")
                    
                else:
                    stats['featured_failed'] += 1
                    print(f"   ❌ Článek {wp_post_id}: {featured_filename} nenalezen v mapování")
            else:
                stats['featured_failed'] += 1
                print(f"   ❌ Článek {wp_post_id}: žádný featured image v původní DB")
        
        mysql_conn.commit()
        
    except Exception as e:
        mysql_conn.rollback()
        print(f"❌ Chyba při opravě featured images: {e}")
    
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()
    
    print(f"\n📊 STATISTIKY FEATURED IMAGES:")
    print(f"   Zpracováno článků: {stats['articles_processed']}")
    print(f"   Opraveno: {stats['featured_fixed']}")
    print(f"   Selhalo: {stats['featured_failed']}")
    print(f"   Úspěšnost: {stats['featured_fixed']/stats['articles_processed']*100:.1f}%")
    
    return stats

def main():
    """Hlavní funkce pro opravu existující migrace"""
    print("🔧 OPRAVA EXISTUJÍCÍ MIGRACE")
    print("=" * 60)
    print("🎯 Opravuje mapování a databázi bez kopírování souborů")
    print()
    
    # 1. Analyzovat nahrané obrázky
    uploaded_images = analyze_uploaded_images()
    
    if not uploaded_images:
        print("❌ Žádné nahrané obrázky nenalezeny!")
        return
    
    # 2. Získat WordPress ID
    wp_images = get_wordpress_image_ids()
    
    if not wp_images:
        print("❌ Žádné WordPress attachmenty nenalezeny!")
        return
    
    # 3. Vytvořit opravené mapování
    fixed_mapping, mapping_stats = create_fixed_image_mapping(uploaded_images, wp_images)
    
    # 4. Uložit opravené mapování
    print(f"\n💾 UKLÁDÁNÍ OPRAVENÉHO MAPOVÁNÍ")
    print("=" * 50)
    
    # Zálohovat současné mapování
    current_mapping = load_mapping('image_map.json')
    save_mapping(current_mapping, 'image_map_backup.json')
    print(f"   ✅ Záloha: image_map_backup.json")
    
    # Uložit opravené mapování
    save_mapping(fixed_mapping, 'image_map.json')
    print(f"   ✅ Nové mapování: {len(fixed_mapping)} záznamů")
    
    # 5. Opravit featured images
    featured_stats = fix_featured_images_from_original_db(fixed_mapping)
    
    # 6. Finální souhrn
    print(f"\n" + "=" * 60)
    print(f"📋 FINÁLNÍ SOUHRN")
    print(f"=" * 60)
    print(f"Nahrané obrázky: {sum(len(imgs) for imgs in uploaded_images.values())}")
    print(f"Opravené mapování: {mapping_stats['mapped']} záznamů")
    print(f"Kvalita obrázků: {mapping_stats['originals_preferred']}/{mapping_stats['mapped']} originálů/velkých")
    print(f"Featured images: {featured_stats['featured_fixed']}/{featured_stats['articles_processed']} opraveno")
    
    quality_percent = mapping_stats['originals_preferred']/mapping_stats['mapped']*100
    featured_percent = featured_stats['featured_fixed']/featured_stats['articles_processed']*100
    
    print(f"\n🎯 VÝSLEDKY:")
    print(f"   Kvalita obrázků: {quality_percent:.1f}%")
    print(f"   Featured images: {featured_percent:.1f}%")
    
    if quality_percent > 80 and featured_percent > 80:
        print(f"\n🎉 OPRAVA BYLA ÚSPĚŠNÁ!")
        print(f"🌐 Zkontrolujte: http://mujdum.test/?p=425714")
    else:
        print(f"\n⚠️  OPRAVA BYLA ČÁSTEČNĚ ÚSPĚŠNÁ")
        print(f"📝 Zkontrolujte logy výše pro detaily")

if __name__ == "__main__":
    main()
