#!/usr/bin/env python3
"""
Ově<PERSON><PERSON> kolik verzí existuje pro obrázky konkrétního článku
"""
import json
import re
import os
from collections import defaultdict
from utils_mujdum import load_mapping

def analyze_image_versions(article_unique_id, image_map):
    """Analyzuje verze obrázků pro konkrétní článek"""
    
    print(f"🔍 ANALÝZA VERZÍ PRO ČLÁNEK: {article_unique_id}")
    print("=" * 60)
    
    # Najít všechny obrázky článku
    article_images = []
    
    for path, info in image_map.items():
        if not isinstance(info, dict):
            continue
            
        if article_unique_id in path:
            filename = os.path.basename(info.get('wp_url', ''))
            
            # Základní název bez rozměrů a hash
            base_name = re.sub(r'_\d+x\d+', '', filename)
            base_name = re.sub(r'-[a-f0-9]{10,}', '', base_name)
            
            # Rozměry
            match = re.search(r'_(\d+)x(\d+)', filename)
            dimensions = f"{match.group(1)}x{match.group(2)}" if match else "ORIGINAL"
            
            article_images.append({
                'wp_id': info.get('wp_id'),
                'filename': filename,
                'base_name': base_name,
                'dimensions': dimensions,
                'path': path
            })
    
    # Seskupit podle základního názvu
    groups = defaultdict(list)
    for img in article_images:
        groups[img['base_name']].append(img)
    
    print(f"📊 NALEZENO {len(article_images)} OBRÁZKŮ v {len(groups)} SKUPINÁCH:")
    print()
    
    for base_name, versions in groups.items():
        print(f"📂 {base_name}:")
        print(f"   🔢 Počet verzí: {len(versions)}")
        
        for v in versions:
            print(f"      • ID {v['wp_id']}: {v['dimensions']} - {v['filename']}")
        print()
    
    # Najít všechny verze každého obrázku v celém mapování
    print("🌍 HLEDÁNÍ DALŠÍCH VERZÍ V CELÉM MAPOVÁNÍ:")
    print("=" * 60)
    
    for base_name in groups.keys():
        print(f"\n🔎 Hledám všechny verze: {base_name}")
        
        all_versions = []
        
        for path, info in image_map.items():
            if not isinstance(info, dict):
                continue
                
            filename = os.path.basename(info.get('wp_url', ''))
            file_base = re.sub(r'_\d+x\d+', '', filename)
            file_base = re.sub(r'-[a-f0-9]{10,}', '', file_base)
            
            if file_base == base_name:
                match = re.search(r'_(\d+)x(\d+)', filename)
                dimensions = f"{match.group(1)}x{match.group(2)}" if match else "ORIGINAL"
                size = int(match.group(1)) * int(match.group(2)) if match else 999999
                
                all_versions.append({
                    'wp_id': info.get('wp_id'),
                    'filename': filename,
                    'dimensions': dimensions,
                    'size': size,
                    'article_id': re.search(r'([a-f0-9]{10,})', path).group(1) if re.search(r'([a-f0-9]{10,})', path) else 'unknown'
                })
        
        # Seřadit podle velikosti
        all_versions.sort(key=lambda x: x['size'], reverse=True)
        
        print(f"   📊 Celkem verzí: {len(all_versions)}")
        
        if len(all_versions) > 1:
            print("   🎯 Největší verze:")
            for i, v in enumerate(all_versions[:3]):
                print(f"      {i+1}. ID {v['wp_id']}: {v['dimensions']} (článek: {v['article_id']})")
        else:
            print("   ⚠️  Existuje jen jedna verze!")

def main():
    """Hlavní funkce"""
    
    article_unique_id = "665510acb770a"
    
    print("🔍 Načítám mapování obrázků...")
    image_map = load_mapping('image_map.json')
    print(f"✅ Načteno {len(image_map)} mapování obrázků")
    print()
    
    analyze_image_versions(article_unique_id, image_map)

if __name__ == "__main__":
    main()
