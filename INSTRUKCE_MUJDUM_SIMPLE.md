# 🚀 Jednoduch<PERSON> migrace MUJDUM-2 - <PERSON><PERSON> obr<PERSON>ů

## 💡 Koncept

Místo kopírování 55 600 obrázků jeden po jednom, jednoduše:
1. **Zkopírujeme celou složku** s obrázky do WordPress
2. **Upravíme jen cesty** v databázi 
3. **Všechno bude fungovat** rychle a efektivně

## 📋 Postup krok za krokem

### Krok 1: Příprava obrázků (PŘED migrací)

```bash
# 1. Zkopírujte celou složku obrázků do WordPress
# Příklad: pokud máte obrázky v /old/path/obrazek/
sudo cp -r /old/path/obrazek/ /path/to/wordpress/wp-content/uploads/

# 2. Nastavte správná práva
sudo chown -R www-data:www-data /path/to/wordpress/wp-content/uploads/obrazek/
sudo chmod -R 755 /path/to/wordpress/wp-content/uploads/obrazek/

# 3. <PERSON><PERSON><PERSON><PERSON><PERSON>, že složka existuje
ls -la /path/to/wordpress/wp-content/uploads/obrazek/
```

### Krok 2: Spuštění migrace

```bash
# Automatická migrace (doporučeno)
python run_migration_mujdum.py

# NEBO manuálně po krocích:
python migrate_categories.py
python migrate_users.py
python migrate_images_mujdum_simple.py
python migrate_articles_mujdum.py
python migrate_galleries_mujdum.py
```

### Krok 3: Ověření výsledků

Po migraci budou obrázky dostupné na URL:
```
http://your-site.com/wp-content/uploads/obrazky/clanek/image1.jpg
http://your-site.com/wp-content/uploads/obrazky/galerie/image2.jpg
```

## 🔧 Jak to funguje

### Struktura cest:

| Původní databáze | WordPress cesta | WordPress URL |
|-----------------|-----------------|---------------|
| `clanek/img1.jpg` | `obrazky/clanek/img1.jpg` | `/wp-content/uploads/obrazky/clanek/img1.jpg` |
| `galerie/img2.jpg` | `obrazky/galerie/img2.jpg` | `/wp-content/uploads/obrazky/galerie/img2.jpg` |

### Co dělají skripty:

1. **migrate_images_mujdum_simple.py**
   - Vytvoří WordPress attachment záznamy
   - Nastaví cesty jako `obrazky/původní_cesta`
   - Nevytváří fyzické soubory (ty jsou už zkopírované)

2. **migrate_articles_mujdum.py**
   - Migruje články
   - Aktualizuje odkazy na obrázky v obsahu
   - Nastaví featured images

3. **migrate_galleries_mujdum.py**
   - Vytvoří galerie z obrázků článků
   - Přidá gallery shortcode do obsahu

## ⚡ Výhody tohoto přístupu:

- **Rychlost**: Kopírování celé složky vs. 55 600 jednotlivých souborů
- **Spolehlivost**: Méně kroků = méně možností chyb
- **Jednoduchost**: Jednodušší údržba a pochopení
- **Flexibilita**: Můžete upravit cesty podle potřeby

## 🔍 Řešení problémů

### Problém: Obrázky se nezobrazují
```bash
# Zkontrolujte práva
ls -la /path/to/wordpress/wp-content/uploads/obrazky/

# Zkontrolujte, že složka existuje
find /path/to/wordpress/wp-content/uploads/obrazky/ -name "*.jpg" | head -5
```

### Problém: Chybí některé obrázky v galeriích
```bash
# Spusťte migraci galerií znovu
python migrate_galleries_mujdum.py

# Nebo pro konkrétní článek
python migrate_galleries_mujdum.py 12345
```

### Problém: Špatné cesty v obsahu článků
```bash
# Spusťte opravu cest obrázků
python fix_post_content_images.py --all
```

## 📊 Očekávané výsledky

Po úspěšné migraci:
- ✅ **6 175** migravených článků
- ✅ **55 600** migravených obrázků (jen databázové záznamy)
- ✅ **X galerií** (závisí na obsahu)
- ✅ **Správně fungující** featured images
- ✅ **Správně fungující** obrázky v obsahu
- ✅ **Správně fungující** galerie

## 🎯 Tipy pro úspěch

1. **Vždy nejprve zkopírujte obrázky** - migrace spoléhá na jejich existenci
2. **Zkontrolujte cesty v .env** - ujistěte se, že WP_UPLOADS_PATH je správný
3. **Testujte na malém vzorku** - použijte limit při testování
4. **Zálohujte databázi** - před spuštěním migrace
5. **Sledujte logy** - migrace vytváří detailní logy

## 🗂️ Vytvořené soubory

- `migrate_images_mujdum_simple.py` - Migrace obrázků bez kopírování
- `migrate_articles_mujdum.py` - Migrace článků s aktualizovanými cestami
- `migrate_galleries_mujdum.py` - Migrace galerií z prefix_obrazek
- `utils_mujdum.py` - Utility funkce pro novou strukturu
- `config_mujdum.py` - Konfigurace pro mujdum-2
- `run_migration_mujdum.py` - Automatický spouštěč všech kroků

Tento přístup je **mnohem efektivnější** než původní a bude fungovat rychle i s velkým množstvím obrázků! 🚀
