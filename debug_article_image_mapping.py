#!/usr/bin/env python3
"""
Detailní debug analýza mapování obrázků pro konkrétní č<PERSON> pro<PERSON> se nedaří najít origin<PERSON>
"""
import json
import re
import os
import logging
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_article_images(article_unique_id, image_map):
    """Debuguje obrázky konkrétního článku"""
    
    print("=" * 80)
    print(f"🔍 DEBUG MAPOVÁNÍ OBRÁZKŮ PRO ČLÁNEK: {article_unique_id}")
    print("=" * 80)
    
    # Najít všechny obrázky tohoto článku
    article_images = []
    
    for path, info in image_map.items():
        if not isinstance(info, dict):
            continue
            
        if article_unique_id in path:
            filename = os.path.basename(info.get('wp_url', ''))
            
            # Analyzovat typ souboru
            has_dimensions = bool(re.search(r'_(\d+)x(\d+)', filename))
            
            if has_dimensions:
                match = re.search(r'_(\d+)x(\d+)', filename)
                dimensions = f"{match.group(1)}x{match.group(2)}"
            else:
                dimensions = "ORIGINAL"
            
            # Získat základní název
            base_name = re.sub(r'_\d+x\d+', '', filename)
            base_name = re.sub(r'-[a-f0-9]{10,}', '', base_name)
            
            article_images.append({
                'path': path,
                'wp_id': info.get('wp_id'),
                'wp_url': info.get('wp_url'),
                'filename': filename,
                'dimensions': dimensions,
                'base_name': base_name,
                'is_thumbnail': has_dimensions,
                'is_original': not has_dimensions
            })
    
    print(f"📊 NALEZENO {len(article_images)} OBRÁZKŮ:")
    print()
    
    # Roztřídit podle typu
    originals = [img for img in article_images if img['is_original']]
    thumbnails = [img for img in article_images if img['is_thumbnail']]
    
    print(f"🎯 ORIGINÁLY ({len(originals)}):")
    for img in originals:
        print(f"   ✅ {img['base_name']} (ID: {img['wp_id']})")
        print(f"      📁 {img['path']}")
        print(f"      🔗 {img['wp_url']}")
        print()
    
    print(f"🔴 NÁHLEDY ({len(thumbnails)}):")
    for img in thumbnails:
        print(f"   📏 {img['base_name']} ({img['dimensions']}) (ID: {img['wp_id']})")
        print(f"      📁 {img['path']}")
        print(f"      🔗 {img['wp_url']}")
        print()
    
    # Analyzovat párování
    print("🔄 ANALÝZA PÁROVÁNÍ:")
    print("=" * 50)
    
    base_names = {}
    for img in article_images:
        base = img['base_name']
        if base not in base_names:
            base_names[base] = {'originals': [], 'thumbnails': []}
        
        if img['is_original']:
            base_names[base]['originals'].append(img)
        else:
            base_names[base]['thumbnails'].append(img)
    
    for base_name, groups in base_names.items():
        print(f"\n📂 SKUPINA: {base_name}")
        print(f"   🎯 Originály: {len(groups['originals'])}")
        print(f"   🔴 Náhledy: {len(groups['thumbnails'])}")
        
        if groups['originals'] and groups['thumbnails']:
            print("   ✅ PÁROVÁNÍ MOŽNÉ!")
            for orig in groups['originals']:
                print(f"      → Originál ID {orig['wp_id']}: {orig['filename']}")
            for thumb in groups['thumbnails']:
                print(f"      → Náhled ID {thumb['wp_id']}: {thumb['filename']}")
        elif groups['thumbnails'] and not groups['originals']:
            print("   ❌ PROBLÉM: Náhledy bez originálů!")
            for thumb in groups['thumbnails']:
                print(f"      → Osamělý náhled: {thumb['filename']}")
        elif groups['originals'] and not groups['thumbnails']:
            print("   ℹ️  Pouze originály (v pořádku)")
    
    # Hledat možné originály s podobnými názvy
    print("\n" + "=" * 50)
    print("🔍 HLEDÁNÍ PODOBNÝCH ORIGINÁLŮ V CELÉM MAPOVÁNÍ:")
    print("=" * 50)
    
    for thumb in thumbnails:
        base_name = thumb['base_name']
        
        print(f"\n🔎 Hledám originál pro: {base_name}")
        
        # Hledat v celém mapování
        similar_originals = []
        
        for path, info in image_map.items():
            if not isinstance(info, dict):
                continue
                
            filename = os.path.basename(info.get('wp_url', ''))
            
            # Jen originály
            if re.search(r'_\d+x\d+', filename):
                continue
                
            # Získat základní název
            other_base = re.sub(r'-[a-f0-9]{10,}', '', filename)
            
            # Porovnat názvy
            if base_name.lower() in other_base.lower() or other_base.lower() in base_name.lower():
                similarity = len(set(base_name.lower()) & set(other_base.lower())) / len(set(base_name.lower()) | set(other_base.lower()))
                
                if similarity > 0.7:  # 70% podobnost
                    similar_originals.append({
                        'filename': filename,
                        'base_name': other_base,
                        'wp_id': info.get('wp_id'),
                        'wp_url': info.get('wp_url'),
                        'path': path,
                        'similarity': similarity
                    })
        
        if similar_originals:
            print(f"   ✅ Nalezeno {len(similar_originals)} podobných originálů:")
            # Seřadit podle podobnosti
            similar_originals.sort(key=lambda x: x['similarity'], reverse=True)
            
            for orig in similar_originals[:3]:  # Top 3
                print(f"      🎯 {orig['similarity']:.1%} - {orig['base_name']} (ID: {orig['wp_id']})")
                print(f"         📁 {orig['path']}")
        else:
            print(f"   ❌ Žádné podobné originály nenalezeny")
    
    return article_images, base_names

def main():
    """Hlavní funkce"""
    
    # ID článku z debug výstupu
    article_unique_id = "665510acb770a"
    article_title = "Přeměňte svou zahradu na stylový relaxační koutek"
    
    print(f"📖 Článek: {article_title}")
    print(f"🆔 Unique ID: {article_unique_id}")
    print()
    
    # Načíst mapování
    logging.info("Načítám mapování obrázků...")
    image_map = load_mapping('image_map.json')
    logging.info(f"Načteno {len(image_map)} mapování obrázků")
    
    # Debugovat
    images, base_names = debug_article_images(article_unique_id, image_map)
    
    # Shrnutí
    print("\n" + "=" * 80)
    print("📋 SHRNUTÍ:")
    print("=" * 80)
    
    originals_count = len([img for img in images if img['is_original']])
    thumbnails_count = len([img for img in images if img['is_thumbnail']])
    
    print(f"📊 Celkem obrázků: {len(images)}")
    print(f"🎯 Originály: {originals_count}")  
    print(f"🔴 Náhledy: {thumbnails_count}")
    
    pairable = 0
    orphaned_thumbnails = 0
    
    for base_name, groups in base_names.items():
        if groups['originals'] and groups['thumbnails']:
            pairable += len(groups['thumbnails'])
        elif groups['thumbnails'] and not groups['originals']:
            orphaned_thumbnails += len(groups['thumbnails'])
    
    print(f"✅ Párované náhledy: {pairable}")
    print(f"❌ Osamělé náhledy: {orphaned_thumbnails}")
    
    if orphaned_thumbnails > 0:
        print()
        print("🚨 PROBLÉM: Některé náhledy nemají odpovídající originály!")
        print("   → Možná řešení:")
        print("   1. Originály nebyly správně migrovány")
        print("   2. Názvy souborů byly změněny během migrace")
        print("   3. Originály jsou uloženy pod jiným názvem")

if __name__ == "__main__":
    main()
