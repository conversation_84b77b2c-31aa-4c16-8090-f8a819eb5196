#!/usr/bin/env python3
"""
Debug skript pro z<PERSON>štění chyběj<PERSON><PERSON><PERSON><PERSON> so<PERSON>or<PERSON>
"""
import os
import logging
from db_connectors import get_pg_connection
from config_mujdum import TBL_OBRAZEK, WP_UPLOADS_PATH

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_missing_files():
    """Najde chybějící soubory a ukáže skutečnou strukturu"""
    
    print("=" * 70)
    print("🔍 DEBUG CHYBĚJÍCÍCH SOUBORŮ")
    print("=" * 70)
    
    # 1. Zkontrolovat konkrétní problematické soubory
    problem_files = [
        'kuzmice-0044.jpg',
        'kuzmice-0018.jpg', 
        'kuzmice-0016.jpg',
        'murali-uf8ruc-l6ss-unsplash-(kopie).jpg'
    ]
    
    print("\n1. HLEDÁNÍ PROBLEMATICKÝCH SOUBORŮ:")
    print("-" * 40)
    
    obrazek_base = os.path.join(WP_UPLOADS_PATH, 'obrazek')
    
    for filename in problem_files:
        print(f"\n🔍 Hledám: {filename}")
        found_paths = []
        
        # Projít všechny složky v obrazek/
        for root, dirs, files in os.walk(obrazek_base):
            if filename in files:
                rel_path = os.path.relpath(os.path.join(root, filename), WP_UPLOADS_PATH)
                found_paths.append(rel_path)
        
        if found_paths:
            print(f"   ✅ Nalezen v:")
            for path in found_paths:
                print(f"      {path}")
        else:
            print(f"   ❌ NENALEZEN!")
    
    # 2. Zkontrolovat databázové záznamy pro tyto soubory
    print(f"\n2. DATABÁZOVÉ ZÁZNAMY:")
    print("-" * 40)
    
    pg_conn = get_pg_connection()
    pg_cursor = pg_conn.cursor()
    
    for filename in problem_files:
        pg_cursor.execute(f"""
            SELECT id_obrazek, soubor, polozka_id, priorita, typ
            FROM {TBL_OBRAZEK} 
            WHERE soubor = %s
        """, (filename,))
        
        records = pg_cursor.fetchall()
        print(f"\n📊 {filename}:")
        if records:
            for img_id, soubor, polozka_id, priorita, typ in records:
                print(f"   ID: {img_id}, polozka_id: {polozka_id}, typ: {typ}, priorita: {priorita}")
        else:
            print(f"   ❌ Žádný záznam v databázi!")
    
    # 3. Ukázat strukturu obrazek složky
    print(f"\n3. STRUKTURA SLOŽKY OBRAZEK:")
    print("-" * 40)
    
    if os.path.exists(obrazek_base):
        print(f"📁 {obrazek_base}")
        
        # Počet podsložek
        subdirs = [d for d in os.listdir(obrazek_base) if os.path.isdir(os.path.join(obrazek_base, d))]
        print(f"   📂 Počet podsložek: {len(subdirs)}")
        
        # Vzorové podsložky
        print(f"   📂 Vzorové podsložky:")
        for subdir in subdirs[:10]:
            subdir_path = os.path.join(obrazek_base, subdir)
            file_count = len([f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))])
            print(f"      {subdir}/ ({file_count} souborů)")
        
        if len(subdirs) > 10:
            print(f"      ... a dalších {len(subdirs) - 10} složek")
        
        # Soubory přímo v obrazek/
        direct_files = [f for f in os.listdir(obrazek_base) if os.path.isfile(os.path.join(obrazek_base, f))]
        if direct_files:
            print(f"   📄 Soubory přímo v obrazek/: {len(direct_files)}")
            for file in direct_files[:5]:
                print(f"      {file}")
            if len(direct_files) > 5:
                print(f"      ... a dalších {len(direct_files) - 5}")
    
    # 4. Najít všechny .jpg soubory v obrazek/
    print(f"\n4. VŠECHNY JPG SOUBORY:")
    print("-" * 40)
    
    all_images = []
    for root, dirs, files in os.walk(obrazek_base):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
                rel_path = os.path.relpath(os.path.join(root, file), WP_UPLOADS_PATH)
                all_images.append(rel_path)
    
    print(f"📊 Celkem nalezeno {len(all_images)} obrázků")
    
    # Ukázat vzorové cesty
    print(f"📄 Vzorové cesty:")
    for img_path in all_images[:10]:
        print(f"   {img_path}")
    
    if len(all_images) > 10:
        print(f"   ... a dalších {len(all_images) - 10}")
    
    pg_cursor.close()
    pg_conn.close()
    
    # 5. Doporučení
    print(f"\n5. DOPORUČENÍ:")
    print("-" * 40)
    print("🔧 Na základě výše uvedených informací:")
    print("   1. Zkontrolujte, zda jsou soubory ve správných složkách")
    print("   2. Možná je potřeba upravit logiku hledání souborů")
    print("   3. Možná jsou soubory v jiné strukture než očekáváme")

if __name__ == "__main__":
    debug_missing_files()
