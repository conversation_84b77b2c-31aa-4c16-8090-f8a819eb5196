#!/usr/bin/env python3
"""
Analýza velikostí obrázků v image_map.json
Diagnóza problému s mal<PERSON><PERSON> n<PERSON> místo originálů
"""
import json
import re
import os
import logging
from collections import defaultdict, Counter
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ImageSizeAnalyzer:
    def __init__(self):
        self.size_stats = defaultdict(int)
        self.small_images = []
        self.medium_images = []
        self.large_images = []
        self.no_size_images = []
        self.article_problems = defaultdict(list)
        
    def extract_dimensions(self, filename):
        """Extrahuje rozměry z názvu souboru"""
        match = re.search(r'_(\d+)x(\d+)', filename)
        if match:
            return int(match.group(1)), int(match.group(2))
        return None, None
    
    def categorize_image(self, filename, wp_url):
        """Kategorizuje obrázek podle velikosti"""
        width, height = self.extract_dimensions(filename)
        
        if width and height:
            size = width * height
            category = None
            
            if width <= 150 or height <= 150:
                category = "small"
                self.small_images.append({
                    'filename': filename,
                    'url': wp_url,
                    'size': f"{width}x{height}",
                    'pixels': size
                })
            elif width <= 400 or height <= 300:
                category = "medium"
                self.medium_images.append({
                    'filename': filename,
                    'url': wp_url,
                    'size': f"{width}x{height}",
                    'pixels': size
                })
            else:
                category = "large"
                self.large_images.append({
                    'filename': filename,
                    'url': wp_url,
                    'size': f"{width}x{height}",
                    'pixels': size
                })
                
            self.size_stats[category] += 1
            return category, width, height
        else:
            # Bez rozměrů - pravděpodobně originál
            category = "original"
            self.no_size_images.append({
                'filename': filename,
                'url': wp_url,
                'size': "original",
                'pixels': 999999
            })
            self.size_stats[category] += 1
            return category, None, None
    
    def extract_article_id(self, path):
        """Extrahuje ID článku z cesty"""
        # Formát: "něco/665510acb770a/soubor.jpg"
        match = re.search(r'([a-f0-9]{10,})', path)
        if match:
            return match.group(1)
        return None
    
    def analyze_mapping(self, image_map):
        """Analyzuje celé mapování obrázků"""
        logging.info("Spouštím analýzu mapování obrázků...")
        
        total_images = len(image_map)
        processed = 0
        
        for path, info in image_map.items():
            processed += 1
            if processed % 1000 == 0:
                logging.info(f"Zpracováno {processed}/{total_images} obrázků...")
            
            if not isinstance(info, dict) or 'wp_url' not in info:
                continue
                
            filename = os.path.basename(info['wp_url'])
            article_id = self.extract_article_id(path)
            
            category, width, height = self.categorize_image(filename, info['wp_url'])
            
            # Zaznamenat problémy podle článků
            if category == "small":
                self.article_problems[article_id].append({
                    'type': 'small_image',
                    'filename': filename,
                    'size': f"{width}x{height}" if width else "unknown",
                    'wp_id': info.get('wp_id'),
                    'path': path
                })
        
        logging.info(f"Analýza dokončena: {processed} obrázků zpracováno")
    
    def find_worst_articles(self, limit=10):
        """Najde články s nejvíce malými obrázky"""
        article_scores = []
        
        for article_id, problems in self.article_problems.items():
            if not article_id:
                continue
                
            small_count = len([p for p in problems if p['type'] == 'small_image'])
            if small_count > 0:
                article_scores.append({
                    'article_id': article_id,
                    'small_images': small_count,
                    'problems': problems
                })
        
        article_scores.sort(key=lambda x: x['small_images'], reverse=True)
        return article_scores[:limit]
    
    def print_statistics(self):
        """Vytiskne detailní statistiky"""
        print("=" * 80)
        print("📊 ANALÝZA VELIKOSTÍ OBRÁZKŮ V MAPOVÁNÍ")
        print("=" * 80)
        
        total = sum(self.size_stats.values())
        
        print(f"📈 CELKOVÉ STATISTIKY:")
        print(f"   Celkem obrázků: {total:,}")
        print()
        
        print(f"🔍 KATEGORIE PODLE VELIKOSTI:")
        for category, count in self.size_stats.items():
            percentage = (count / total * 100) if total > 0 else 0
            print(f"   {category.upper():>8}: {count:>6,} ({percentage:5.1f}%)")
        
        print()
        print(f"⚠️  PROBLÉMOVÉ STATISTIKY:")
        print(f"   Malé náhledy: {len(self.small_images):,} (problém!)")
        print(f"   Střední velikosti: {len(self.medium_images):,}")
        print(f"   Velké obrázky: {len(self.large_images):,}")
        print(f"   Originály: {len(self.no_size_images):,}")
        
        # Top nejmenší obrázky
        if self.small_images:
            print()
            print(f"🔴 TOP 10 NEJMENŠÍCH OBRÁZKŮ:")
            smallest = sorted(self.small_images, key=lambda x: x['pixels'])[:10]
            for i, img in enumerate(smallest, 1):
                print(f"   {i:2d}. {img['size']:>10} - {img['filename'][:50]}")
        
        # Největší obrázky pro srovnání
        if self.large_images:
            print()
            print(f"✅ TOP 5 NEJVĚTŠÍCH OBRÁZKŮ:")
            largest = sorted(self.large_images, key=lambda x: x['pixels'], reverse=True)[:5]
            for i, img in enumerate(largest, 1):
                print(f"   {i:2d}. {img['size']:>10} - {img['filename'][:50]}")
        
        # Problémové články
        worst_articles = self.find_worst_articles(10)
        if worst_articles:
            print()
            print(f"🎯 TOP 10 ČLÁNKŮ S NEJVÍCE MALÝMI OBRÁZKY:")
            for i, article in enumerate(worst_articles, 1):
                print(f"   {i:2d}. Článek {article['article_id']}: {article['small_images']} malých obrázků")
        
        print()
        print("=" * 80)
        
        # Doporučení
        small_percentage = (len(self.small_images) / total * 100) if total > 0 else 0
        
        if small_percentage > 30:
            print("🚨 KRITICKÝ PROBLÉM: Více než 30% obrázků jsou malé náhledy!")
            print("   → Nutné najít a doplnit originální verze")
        elif small_percentage > 10:
            print("⚠️  VAROVÁNÍ: Více než 10% obrázků jsou malé náhledy")
            print("   → Doporučuji najít lepší verze")
        else:
            print("✅ V pořádku: Většina obrázků má odpovídající velikost")
        
        return {
            'total': total,
            'small_count': len(self.small_images),
            'small_percentage': small_percentage,
            'worst_articles': worst_articles
        }

def main():
    """Hlavní funkce"""
    print("🔍 Načítám mapování obrázků...")
    
    try:
        image_map = load_mapping('image_map.json')
        print(f"✅ Načteno {len(image_map):,} mapování obrázků")
        
        analyzer = ImageSizeAnalyzer()
        analyzer.analyze_mapping(image_map)
        
        results = analyzer.print_statistics()
        
        # Uložit problémové články pro další analýzu
        worst_articles = results['worst_articles']
        if worst_articles:
            with open('problematic_articles.json', 'w', encoding='utf-8') as f:
                json.dump(worst_articles, f, indent=2, ensure_ascii=False)
            print(f"💾 Uloženo {len(worst_articles)} problémových článků do 'problematic_articles.json'")
        
        return results
        
    except Exception as e:
        logging.error(f"Chyba při analýze: {e}")
        return None

if __name__ == "__main__":
    main()
