#!/usr/bin/env python3
import logging
import os
import mysql.connector
from datetime import datetime
import json
import sys
import re
from db_connectors import get_pg_connection, get_mysql_connection

# Použijeme nový config pro mujdum
from config_mujdum import (
    TBL_CLANEK, TBL_OBRAZEK, TBL_FOTOGALERIE,
    TBL_WP_POSTS, TBL_WP_POSTMETA,
    DEFAULT_WP_USER_ID, WP_SITE_URL,
    OLD_IMAGE_BASE_PATH
)
from utils_mujdum import (
    load_mapping, save_mapping, format_wp_datetime
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def migrate_galleries_mujdum(target_article_id=None):
    """
    Migruje galerie z nové databáze mujdum-2 do WordPress.
    
    KLÍČOVÝ ROZDÍL: V nové databázi nejsou tabulky prefix_galerie a prefix_galerie_polozka!
    Galerie se řeší pouze přes prefix_obrazek s polozka_id = unikatni_id článku.
    
    Proces:
    1. Načte mapování obrázků a článků
    2. Získá články a jejich unikatni_id
    3. Pro každý článek najde obrázky s odpovídajícím polozka_id
    4. Vytvoří WordPress gallery shortcode pouze z prefix_obrazek
    5. Aktualizuje obsah článku přidáním galerie
    """
    logging.info("Spouštím migraci galerií pro databázi mujdum-2...")
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()

    # Načíst mapování
    image_map = load_mapping('image_map.json')
    article_map = load_mapping('article_map.json')
    gallery_map = load_mapping('gallery_map.json') if os.path.exists(os.path.join('mappings', 'gallery_map.json')) else {}
    
    # Počítadla
    new_galleries_count = 0
    failed_galleries_count = 0
    
    try:
        # 1. Získáme všechny články s jejich unikatni_id
        query = f"""
            SELECT id_clanek, unikatni_id, nazev, nazev_galerie 
            FROM {TBL_CLANEK} 
            WHERE unikatni_id IS NOT NULL AND unikatni_id != ''
        """
        
        # Pokud je zadáno konkrétní ID článku, omezíme výběr jen na něj
        if target_article_id:
            query += f" AND id_clanek = {target_article_id}"
            logging.info(f"Cílení pouze na článek ID {target_article_id}")
            
        pg_cursor.execute(query)
        articles = pg_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles)} článků s unikatním ID.")
        
        # 2. Pro každý článek vytvoříme galerii z obrázků se stejným polozka_id
        for article_id, unique_id, article_title, nazev_galerie in articles:
            try:
                # Kontrola, zda byl článek migrován do WordPress
                if str(article_id) not in article_map:
                    logging.warning(f"Článek ID {article_id} ({article_title}) nebyl migrován, přeskakuji.")
                    continue
                
                wp_post_id = article_map[str(article_id)]
                
                # Kontrola, zda již byla pro tento článek vytvořena galerie
                gallery_key = f"article_{article_id}"
                if gallery_key in gallery_map:
                    logging.info(f"Galerie pro článek ID {article_id} již byla zpracována, přeskakuji.")
                    continue
                
                # 3. Získat obrázky z tabulky obrazek pro tento článek
                # Podle analýzy: typ 0 = featured images, typ 1 = galerie, typ 2 = ostatní
                pg_cursor.execute(f"""
                    SELECT id_obrazek, soubor, popisek, priorita, typ 
                    FROM {TBL_OBRAZEK} 
                    WHERE polozka_id = %s AND active_state = 1 AND typ = 1
                    ORDER BY priorita DESC, id_obrazek
                """, (unique_id,))
                gallery_items = pg_cursor.fetchall()
                
                # Pokud nejsou obrázky typu 1, zkusíme všechny a vyloučíme featured image
                if not gallery_items:
                    pg_cursor.execute(f"""
                        SELECT id_obrazek, soubor, popisek, priorita, typ 
                        FROM {TBL_OBRAZEK} 
                        WHERE polozka_id = %s AND active_state = 1
                        ORDER BY priorita DESC, id_obrazek
                    """, (unique_id,))
                    all_images = pg_cursor.fetchall()
                    
                    if all_images:
                        # Vyloučit featured image z galerie (pokud existuje)
                        # Získat název featured image z článku
                        if article_id in [row[0] for row in articles]:
                            # Najít článek a jeho obrazek_src
                            pg_cursor.execute(f"""
                                SELECT obrazek_src FROM {TBL_CLANEK} 
                                WHERE id_clanek = %s
                            """, (article_id,))
                            featured_result = pg_cursor.fetchone()
                            featured_src = featured_result[0] if featured_result else None
                            
                            if featured_src:
                                featured_filename = os.path.basename(featured_src)
                                # Vyloučit obrázky se stejným názvem jako featured image
                                gallery_items = [
                                    img for img in all_images 
                                    if os.path.basename(img[1]) != featured_filename
                                ]
                                logging.info(f"Pro článek ID {article_id} vyloučen featured image '{featured_filename}' z galerie.")
                            else:
                                gallery_items = all_images[1:] if len(all_images) > 1 else []
                        else:
                            gallery_items = all_images[1:] if len(all_images) > 1 else []
                        
                        if not gallery_items:
                            logging.info(f"Pro článek ID {article_id} ({article_title}) po vyloučení featured image nezbývají žádné obrázky pro galerii.")
                            continue
                    else:
                        logging.info(f"Pro článek ID {article_id} ({article_title}) nebyl nalezen žádný obrázek.")
                        continue
                
                gallery_title = nazev_galerie if nazev_galerie else f"Galerie - {article_title}"
                logging.info(f"Pro článek ID {article_id} ({article_title}) nalezeno {len(gallery_items)} obrázků pro galerii '{gallery_title}'.")
                
                # 4. Vytvořit WordPress galerii
                gallery_ids = []
                missing_mappings = []
                used_attachment_ids = set()  # Pro prevenci duplikátů
                
                for _, image_file, image_desc, _, image_type in gallery_items:
                    attachment_id = None
                    
                    # Nejprve zkusit najít podle polozka_id/soubor (PRIMÁRNÍ KLÍČ)
                    full_key = f"{unique_id}/{image_file}"
                    if full_key in image_map:
                        attachment_id = image_map[full_key]['wp_id']
                        logging.debug(f"Nalezen obrázek podle polozka_id/soubor: {full_key}")
                    
                    # Pokud nenalezen, zkusit jen podle názvu souboru (FALLBACK)
                    elif image_file in image_map:
                        attachment_id = image_map[image_file]['wp_id']
                        logging.debug(f"Nalezen obrázek podle názvu souboru: {image_file}")
                    
                    # Pokud stále nenalezen, zkusit hledat v celém mapování
                    else:
                        for mapped_path, mapped_info in image_map.items():
                            if isinstance(mapped_info, dict) and 'wp_id' in mapped_info:
                                # Zkusit různé kombinace
                                if (mapped_path == full_key or 
                                    mapped_path.endswith(f"/{image_file}") or
                                    (os.path.basename(mapped_path) == image_file and unique_id in mapped_path)):
                                    attachment_id = mapped_info['wp_id']
                                    logging.debug(f"Nalezen obrázek v mapování: {mapped_path}")
                                    break
                    
                    if attachment_id:
                        # Kontrola duplikátů
                        if attachment_id not in used_attachment_ids:
                            gallery_ids.append(str(attachment_id))
                            used_attachment_ids.add(attachment_id)
                        else:
                            logging.debug(f"Přeskočen duplikát attachment ID {attachment_id} pro obrázek '{image_file}'")
                    else:
                        missing_mappings.append(image_file)
                        logging.warning(f"Obrázek '{image_file}' není v mapování.")

                if missing_mappings:
                    logging.warning(f"Pro článek ID {article_id} chybí mapování pro {len(missing_mappings)} obrázků: {', '.join(missing_mappings[:3])}{'...' if len(missing_mappings) > 3 else ''}")

                if not gallery_ids:
                    logging.warning(f"Pro článek ID {article_id} ({article_title}) nebyly nalezeny žádné migrovatelné obrázky, přeskakuji.")
                    failed_galleries_count += 1
                    continue
                
                # 5. Vytvořit gallery shortcode
                gallery_shortcode = f'[gallery ids="{",".join(gallery_ids)}"]'
                
                # 6. Získat stávající obsah článku
                mysql_cursor.execute(f"SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s", (wp_post_id,))
                current_content = mysql_cursor.fetchone()[0]
                
                # 7. Přidat galerii na konec nebo po značce <!-- gallery -->
                if "<!-- gallery -->" in current_content:
                    new_content = current_content.replace("<!-- gallery -->", f"<!-- gallery -->\n{gallery_shortcode}")
                else:
                    new_content = current_content + "\n\n" + gallery_shortcode
                
                # 8. Aktualizovat obsah článku
                mysql_cursor.execute(f"UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s", (new_content, wp_post_id))
                
                # 9. Uložit metadata o galerii
                meta_value = {
                    'original_article_id': article_id,
                    'original_unique_id': unique_id,
                    'article_title': article_title,
                    'gallery_title': gallery_title,
                    'image_ids': gallery_ids,
                    'image_count': len(gallery_ids),
                    'database_version': 'mujdum-2'
                }
                
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (wp_post_id, 'sabre_gallery', json.dumps(meta_value))
                )
                
                # 10. Aktualizovat mapování
                gallery_map[gallery_key] = {
                    'wp_post_id': wp_post_id,
                    'gallery_shortcode': gallery_shortcode,
                    'image_count': len(gallery_ids),
                    'gallery_title': gallery_title
                }
                
                mysql_conn.commit()
                new_galleries_count += 1
                logging.info(f"Galerie pro článek ID {article_id} ({article_title}) úspěšně vytvořena s {len(gallery_ids)} obrázky")
                
            except Exception as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při vytváření galerie pro článek ID {article_id}: {e}")
                failed_galleries_count += 1
        
        # Uložit mapování
        save_mapping(gallery_map, 'gallery_map.json')
        
        # Poznámka: V nové databázi mujdum-2 neexistuje tabulka prefix_galerie ani prefix_galerie_polozka,
        # takže přeskakujeme druhou část původního skriptu
        logging.info("Poznámka: V databázi mujdum-2 neexistují tabulky prefix_galerie a prefix_galerie_polozka.")
        
    except Exception as e:
        logging.error(f"Obecná chyba v migrate_galleries_mujdum: {e}")
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        logging.info(f"Migrace galerií pro mujdum-2 dokončena. Zpracováno celkem {new_galleries_count} nových galerií. Selhalo: {failed_galleries_count}.")

if __name__ == "__main__":
    # Kontrola argumentů pro cílení na konkrétní článek
    target_article_id = None
    
    # Zpracování argumentů příkazové řádky
    for i, arg in enumerate(sys.argv[1:], 1):
        try:
            target_article_id = int(arg)
            print(f"Cílový článek ID: {target_article_id}")
        except ValueError:
            if '=' in arg:  # Formát klíč=hodnota
                key, value = arg.split('=', 1)
                if key == 'id':
                    try:
                        target_article_id = int(value)
                        print(f"Cílový článek ID: {target_article_id}")
                    except ValueError:
                        print(f"Neplatné ID článku: {value}")
            else:
                print(f"Neplatný argument: {arg}")
    
    migrate_galleries_mujdum(target_article_id)
