#!/usr/bin/env python3
"""
Bezpe<PERSON>n<PERSON> testovací skript pro ověř<PERSON><PERSON> fix_final_mujdum_issues_fast.py
"""
import json
import os
import mysql.connector
from db_connectors import get_mysql_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA

def create_database_backup():
    """Vyt<PERSON>ří z<PERSON>hu kritických tabulek"""
    print("💾 VYTVÁŘENÍ ZÁLOHY DATABÁZE...")
    
    try:
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor()
        
        # Záloha wp_posts (pouze attachments)
        mysql_cursor.execute(f"""
            CREATE TABLE IF NOT EXISTS {TBL_WP_POSTS}_backup_before_fix AS 
            SELECT * FROM {TBL_WP_POSTS} WHERE post_type = 'attachment'
        """)
        
        # <PERSON><PERSON>loha wp_postmeta (pouze pro attachments)
        mysql_cursor.execute(f"""
            CREATE TABLE IF NOT EXISTS {TBL_WP_POSTMETA}_backup_before_fix AS 
            SELECT pm.* FROM {TBL_WP_POSTMETA} pm
            JOIN {TBL_WP_POSTS} p ON pm.post_id = p.ID
            WHERE p.post_type = 'attachment'
        """)
        
        mysql_conn.commit()
        mysql_cursor.close()
        mysql_conn.close()
        
        print("   ✓ Záloha vytvořena")
        return True
        
    except Exception as e:
        print(f"   ✗ Chyba při vytváření zálohy: {e}")
        return False

def select_test_articles():
    """Vybere vhodné články pro testování"""
    print("🎯 VÝBĚR TESTOVACÍCH ČLÁNKŮ...")
    
    try:
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor()
        
        # Najít články s galeriemi a známým polozka_id
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, pm.meta_value as polozka_id, p.post_content
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'post' 
            AND pm.meta_key = 'sabre_unikatni_id'
            AND pm.meta_value IS NOT NULL 
            AND pm.meta_value != ''
            AND p.post_content LIKE '%[gallery%'
            ORDER BY CHAR_LENGTH(p.post_content) DESC
            LIMIT 10
        """)
        
        test_articles = mysql_cursor.fetchall()
        
        print(f"   Nalezeno {len(test_articles)} vhodných článků pro test:")
        for i, (post_id, title, polozka_id, content) in enumerate(test_articles[:5]):
            gallery_count = content.count('[gallery')
            print(f"   {i+1}. ID {post_id}: '{title[:50]}...' (polozka_id: {polozka_id}, {gallery_count} galerií)")
        
        mysql_cursor.close()
        mysql_conn.close()
        
        return test_articles[:3]  # Vrátit jen první 3 pro test
        
    except Exception as e:
        print(f"   ✗ Chyba při výběru článků: {e}")
        return []

def analyze_article_images(post_id, polozka_id):
    """Analyzuje obrázky konkrétního článku"""
    print(f"\n🔍 ANALÝZA ČLÁNKU {post_id} (polozka_id: {polozka_id})")
    
    try:
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor()
        
        # Najít všechny obrázky s tímto polozka_id
        mysql_cursor.execute(f"""
            SELECT p.ID, p.guid, pm.meta_value as polozka_id
            FROM {TBL_WP_POSTS} p
            JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'attachment'
            AND pm.meta_key = 'sabre_polozka_id'
            AND pm.meta_value = %s
        """, (polozka_id,))
        
        article_images = mysql_cursor.fetchall()
        print(f"   Nalezeno {len(article_images)} obrázků pro tento článek")
        
        # Najít featured image
        mysql_cursor.execute(f"""
            SELECT pm.meta_value as thumb_id
            FROM {TBL_WP_POSTMETA} pm
            WHERE pm.post_id = %s AND pm.meta_key = '_thumbnail_id'
        """, (post_id,))
        
        featured_result = mysql_cursor.fetchone()
        featured_id = featured_result[0] if featured_result else None
        
        if featured_id:
            # Zkontrolovat, zda featured image patří do stejného článku
            mysql_cursor.execute(f"""
                SELECT pm.meta_value as polozka_id
                FROM {TBL_WP_POSTMETA} pm
                WHERE pm.post_id = %s AND pm.meta_key = 'sabre_polozka_id'
            """, (featured_id,))
            
            featured_polozka = mysql_cursor.fetchone()
            featured_polozka_id = featured_polozka[0] if featured_polozka else None
            
            if featured_polozka_id == polozka_id:
                print(f"   ✓ Featured image {featured_id} patří do správného článku")
            else:
                print(f"   ⚠️  Featured image {featured_id} patří do jiného článku ({featured_polozka_id})")
        else:
            print(f"   ℹ️  Článek nemá featured image")
        
        # Analyzovat galerie
        mysql_cursor.execute(f"""
            SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s
        """, (post_id,))
        
        content_result = mysql_cursor.fetchone()
        if content_result:
            content = content_result[0]
            import re
            gallery_matches = re.findall(r'\[gallery[^\]]*ids="([^"]+)"[^\]]*\]', content)
            
            for i, ids_string in enumerate(gallery_matches):
                ids = [id.strip() for id in ids_string.split(',') if id.strip().isdigit()]
                print(f"   Galerie {i+1}: {len(ids)} obrázků")
                
                # Zkontrolovat první 3 obrázky z galerie
                for img_id in ids[:3]:
                    mysql_cursor.execute(f"""
                        SELECT pm.meta_value as polozka_id
                        FROM {TBL_WP_POSTMETA} pm
                        WHERE pm.post_id = %s AND pm.meta_key = 'sabre_polozka_id'
                    """, (img_id,))
                    
                    img_polozka = mysql_cursor.fetchone()
                    img_polozka_id = img_polozka[0] if img_polozka else None
                    
                    if img_polozka_id == polozka_id:
                        status = "✓"
                    elif img_polozka_id:
                        status = f"⚠️ ({img_polozka_id})"
                    else:
                        status = "❓ (unknown)"
                    
                    print(f"     - Obrázek {img_id}: {status}")
        
        mysql_cursor.close()
        mysql_conn.close()
        
        return len(article_images), featured_id
        
    except Exception as e:
        print(f"   ✗ Chyba při analýze: {e}")
        return 0, None

def run_dry_run_test():
    """Spustí dry-run test na vybraných článcích"""
    print("\n🧪 DRY-RUN TEST")
    print("=" * 50)
    
    # Vybrat testovací články
    test_articles = select_test_articles()
    
    if not test_articles:
        print("   ✗ Nepodařilo se najít vhodné testovací články")
        return False
    
    print(f"\n📋 PŘED SPUŠTĚNÍM OPRAV:")
    before_stats = {}
    
    for post_id, title, polozka_id, content in test_articles:
        image_count, featured_id = analyze_article_images(post_id, polozka_id)
        before_stats[post_id] = {
            'title': title,
            'polozka_id': polozka_id,
            'image_count': image_count,
            'featured_id': featured_id
        }
    
    return before_stats, test_articles

def verify_fix_results(before_stats, test_articles):
    """Ověří výsledky po spuštění fix skriptu"""
    print(f"\n📋 PO SPUŠTĚNÍ OPRAV:")
    
    improvements = 0
    issues = 0
    
    for post_id, title, polozka_id, content in test_articles:
        print(f"\n🔍 Kontrola článku {post_id}:")
        image_count, featured_id = analyze_article_images(post_id, polozka_id)
        
        before = before_stats[post_id]
        
        if featured_id != before['featured_id']:
            print(f"   📸 Featured image změněn: {before['featured_id']} -> {featured_id}")
            improvements += 1
        
        if image_count != before['image_count']:
            print(f"   🖼️  Počet obrázků změněn: {before['image_count']} -> {image_count}")
    
    print(f"\n📊 SOUHRN ZMĚN:")
    print(f"   Zlepšení: {improvements}")
    print(f"   Problémy: {issues}")
    
    return improvements > 0

def main():
    """Hlavní testovací funkce"""
    print("🧪 BEZPEČNÝ TEST FIX SKRIPTU")
    print("=" * 60)
    
    # 1. Vytvoř zálohu
    if not create_database_backup():
        print("❌ Nepodařilo se vytvořit zálohu. Test přerušen.")
        return
    
    # 2. Spusť dry-run
    result = run_dry_run_test()
    if not result:
        print("❌ Dry-run test selhal.")
        return
    
    before_stats, test_articles = result
    
    # 3. Zobraz instrukce
    print(f"\n📋 INSTRUKCE PRO SPUŠTĚNÍ:")
    print("=" * 50)
    print("1. Zkontrolujte výše uvedené problémy")
    print("2. Spusťte: python fix_final_mujdum_issues_fast.py")
    print("3. Po dokončení spusťte: python test_fix_script_safely.py --verify")
    print()
    print("⚠️  DŮLEŽITÉ:")
    print("   - Záloha byla vytvořena v tabulkách *_backup_before_fix")
    print("   - V případě problémů můžete data obnovit")
    print("   - Doporučuji spustit fix script v off-peak hodinách")
    
    # Uložit before_stats pro pozdější porovnání
    with open('test_results_before.json', 'w') as f:
        json.dump(before_stats, f, indent=2)
    
    print(f"\n✅ Test připraven. Výsledky uloženy do test_results_before.json")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == '--verify':
        # Ověření po spuštění fix skriptu
        try:
            with open('test_results_before.json', 'r') as f:
                before_stats = json.load(f)
            
            # Simulace test_articles (v reálném případě by se načetly)
            print("🔍 OVĚŘENÍ VÝSLEDKŮ PO SPUŠTĚNÍ FIX SKRIPTU")
            print("Spusťte prosím manuální kontrolu vybraných článků na webu.")
            
        except FileNotFoundError:
            print("❌ Soubor test_results_before.json nenalezen. Spusťte nejprve základní test.")
    else:
        main()
