#!/usr/bin/env python3
"""
Debug skript pro zjištění problémů s URL v obsahu článků
"""
import logging
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import TBL_CLANEK, TBL_WP_POSTS, TBL_WP_POSTMETA
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_content_issues():
    """Zjistí problémy s URL obrázků v obsahu článků"""
    
    print("=" * 70)
    print("🔍 DEBUG OBSAHU ČLÁNKŮ")
    print("=" * 70)
    
    # 1. Zkontrolovat původní obsah v PostgreSQL
    print("\n1. PŮVODNÍ OBSAH (PostgreSQL):")
    print("-" * 40)
    
    pg_conn = get_pg_connection()
    pg_cursor = pg_conn.cursor()
    
    # Vzorové články s obrázky v obsahu
    pg_cursor.execute(f"""
        SELECT id_clanek, nazev, text 
        FROM {TBL_CLANEK} 
        WHERE text LIKE '%<img%' OR text LIKE '%.jpg%' OR text LIKE '%.png%'
        LIMIT 3
    """)
    
    original_articles = pg_cursor.fetchall()
    print(f"📄 Nalezeno {len(original_articles)} článků s obrázky v obsahu:")
    
    for article_id, title, content in original_articles:
        print(f"\n📖 Článek ID {article_id}: {title[:50]}...")
        
        # Najít img tagy
        import re
        img_tags = re.findall(r'<img[^>]*>', content)
        if img_tags:
            print("   🖼️ IMG tagy:")
            for i, img in enumerate(img_tags[:2]):  # jen první 2
                print(f"      {i+1}. {img[:100]}...")
        
        # Najít odkazy na obrázky
        image_refs = re.findall(r'["\']([^"\']*\.(?:jpg|jpeg|png|gif))["\']', content, re.IGNORECASE)
        if image_refs:
            print("   🔗 Odkazy na obrázky:")
            for ref in set(image_refs[:5]):  # unikátní, max 5
                print(f"      - {ref}")
    
    pg_cursor.close()
    pg_conn.close()
    
    # 2. Zkontrolovat WordPress obsah
    print(f"\n2. WORDPRESS OBSAH (MySQL):")
    print("-" * 40)
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    # Vzorové migrované články
    mysql_cursor.execute(f"""
        SELECT p.ID, p.post_title, p.post_content
        FROM {TBL_WP_POSTS} p
        JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'post' 
        AND pm.meta_key = 'sabre_database_version' 
        AND pm.meta_value = 'mujdum-2'
        AND (p.post_content LIKE '%<img%' OR p.post_content LIKE '%.jpg%')
        LIMIT 3
    """)
    
    wp_articles = mysql_cursor.fetchall()
    print(f"📄 Nalezeno {len(wp_articles)} WordPress článků s obrázky:")
    
    for post_id, title, content in wp_articles:
        print(f"\n📖 WordPress Post ID {post_id}: {title[:50]}...")
        
        # Najít img tagy
        img_tags = re.findall(r'<img[^>]*>', content)
        if img_tags:
            print("   🖼️ IMG tagy:")
            for i, img in enumerate(img_tags[:2]):
                print(f"      {i+1}. {img[:150]}...")
        
        # Najít problematické URL
        problem_urls = re.findall(r'(http://[^"\']*obrazek[^"\']*obrazek[^"\']*)', content)
        if problem_urls:
            print("   ❌ PROBLEMATICKÉ URL (duplicitní):")
            for url in problem_urls[:3]:
                print(f"      - {url[:100]}...")
    
    # 3. Zkontrolovat featured images
    print(f"\n3. FEATURED IMAGES:")
    print("-" * 40)
    
    mysql_cursor.execute(f"""
        SELECT p.ID, p.post_title, pm.meta_value as thumbnail_id,
               att.guid as thumbnail_url
        FROM {TBL_WP_POSTS} p
        JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
        LEFT JOIN {TBL_WP_POSTS} att ON pm.meta_value = att.ID
        WHERE p.post_type = 'post' 
        AND pm.meta_key = '_thumbnail_id'
        AND p.ID IN (
            SELECT post_id FROM {TBL_WP_POSTMETA} 
            WHERE meta_key = 'sabre_database_version' 
            AND meta_value = 'mujdum-2'
        )
        LIMIT 5
    """)
    
    featured_images = mysql_cursor.fetchall()
    print(f"🖼️ Nalezeno {len(featured_images)} featured images:")
    
    for post_id, title, thumb_id, thumb_url in featured_images:
        print(f"   Post {post_id}: {title[:30]}... -> Thumbnail ID: {thumb_id}")
        if thumb_url:
            print(f"      URL: {thumb_url}")
        else:
            print("      ❌ URL chybí!")
    
    # 4. Zkontrolovat mapování
    print(f"\n4. IMAGE MAPOVÁNÍ:")
    print("-" * 40)
    
    image_map = load_mapping('image_map.json')
    print(f"📊 Celkově {len(image_map)} záznamů v mapování")
    
    sample_mappings = list(image_map.items())[:5]
    for old_path, new_info in sample_mappings:
        print(f"   {old_path} -> {new_info}")
    
    mysql_cursor.close()
    mysql_conn.close()

if __name__ == "__main__":
    debug_content_issues()
