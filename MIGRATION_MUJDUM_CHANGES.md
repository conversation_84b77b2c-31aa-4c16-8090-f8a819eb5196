# Migrace pro datab<PERSON>zi MUJDUM-2 - <PERSON><PERSON><PERSON><PERSON> změn

## 🔍 Identifikované rozdíly mezi databázemi

### Původní databáze "dumabyt" vs. Nov<PERSON> databáze "mujdum-2"

| Aspekt | Původní (dumabyt) | Nová (mujdum-2) | Status |
|--------|-------------------|-----------------|--------|
| **Galerie tabulky** | `prefix_galerie`, `prefix_galerie_polozka` | `prefix_fotogalerie` (prázdná) | ❌ **ZMĚNA** |
| **Článkové galerie** | Přes `prefix_galerie_polozka` | Přes `prefix_obrazek` s `polozka_id` | ❌ **ZMĚNA** |
| **Název galerie** | V `prefix_galerie.nazev` | V `prefix_clanek.nazev_galerie` | ❌ **ZMĚNA** |
| **Osta<PERSON>n<PERSON> ta<PERSON>** | `prefix_clanek`, `prefix_obrazek`, `prefix_rubrika` | Stejné | ✅ **STEJNÉ** |

### Datová statistika (mujdum-2):
- **Články**: 6 175 aktivních
- **Obrázky**: 55 600 aktivních  
- **Fotogalerie**: 0 (tabulka existuje, ale je prázdná)

## 🛠️ Vytvořené úpravy

### 1. Nový konfigurační soubor: `config_mujdum.py`
- Odstraněny odkazy na neexistující tabulky galerií (`TBL_GALERIE`, `TBL_GALERIE_POLOZKA`)
- Přidána `TBL_FOTOGALERIE` (pro budoucí použití, ale je prázdná)
- Komentáře o změnách v databázové struktuře

### 2. Nový migrační skript: `migrate_galleries_mujdum.py`
**Klíčové změny oproti původnímu `migrate_galleries.py`:**

#### ❌ Odstraněno:
- Migrace z `prefix_galerie` a `prefix_galerie_polozka` (neexistují)
- Druhá část skriptu pro samostatné galerie

#### ✅ Přidáno/Upraveno:
- Import `config_mujdum` místo `config`
- Načítání sloupce `nazev_galerie` z `prefix_clanek`
- Logika pro články s jediným obrázkem (použije se jako featured image)
- Značení v metadatech: `'database_version': 'mujdum-2'`

### 3. Nový migrační skript: `migrate_articles_mujdum.py`
**Klíčové změny oproti původnímu `migrate_articles.py`:**

#### ❌ Odstraněno:
- Sloupec `zdroj` (neexistuje v databázi mujdum-2)
- Metadata `sabre_zdroj`

#### ✅ Přidáno/Upraveno:
- Import `config_mujdum` místo `config`
- Načítání sloupce `nazev_galerie` z `prefix_clanek`
- Nové metadata: `sabre_nazev_galerie`, `sabre_database_version`

### 4. Nový migrační skript: `migrate_images_mujdum.py`
**Klíčové změny oproti původnímu `migrate_images.py`:**

#### ❌ Odstraněno:
- Migrace obrázků z `prefix_galerie_polozka` (tabulka neexistuje)
- Sekce 6 původního skriptu

#### ✅ Přidáno/Upraveno:
- Import `config_mujdum` místo `config`
- Nové metadata: `sabre_source`, `sabre_polozka_id`
- Rozlišení zdroje obrázku (`article_image_mujdum2` vs `obrazek_table_mujdum2`)

## 🚀 Postup migrace pro MUJDUM-2

### Krok 1: Aktualizace importů v ostatních souborech

Ostatní migrační skripty je třeba upravit, aby používaly `config_mujdum.py`:

```python
# ZMĚNIT V TĚCHTO SOUBORECH:
# - migrate_articles.py
# - migrate_categories.py  
# - migrate_images.py
# - migrate_users.py

# Z:
from config import (...)

# NA:
from config_mujdum import (...)
```

### Krok 2: Spuštění migrace

```bash
# 1. Migrace kategorií
python migrate_categories.py

# 2. Migrace uživatelů  
python migrate_users.py

# 3. Migrace obrázků
python migrate_images.py

# 4. Migrace článků
python migrate_articles.py

# 5. Migrace galerií (NOVÝ SKRIPT)
python migrate_galleries_mujdum.py
```

### Krok 3: Opravné skripty (volitelné)

Po základní migraci můžete spustit existující opravné skripty:

```bash
# Oprava galerií
python fix_all_galleries_systematic.py

# Oprava obrázků v obsahu
python fix_post_content_images.py --all
```

## ⚠️ Důležité poznámky

### Galerie v nové databázi:
1. **Neexistují samostatné galerie** - vše se řeší přes obrázky článků
2. **Typ obrázku** má význam:
   - `typ = 0`: Úvodní obrázek článku (featured image)
   - `typ = 1`: Obrázky pro galerii
3. **Název galerie** je uložen přímo v článku (`nazev_galerie`)

### Mapování obrázků:
- Obrázky se mapují podle `polozka_id = unikatni_id` článku
- Pokud článek má pouze 1 obrázek → žádná galerie  
- Pokud má více obrázků → první jako featured, zbytek do galerie

## 🔧 Řešení problémů

### Problém: Chybí mapování obrázků
**Řešení**: Ujistěte se, že migrace obrázků proběhla úspěšně před migrací galerií.

### Problém: Galerie se nevytváří
**Možné příčiny**:
1. Článek má pouze 1 obrázek → normální chování
2. Chybí mapování obrázků → zkontrolujte `image_map.json`
3. Obrázky mají `active_state = 0` → zkontrolujte databázi

### Problém: Import error s `config_mujdum`
**Řešení**: Ujistěte se, že všechny potřebné soubory importují `config_mujdum` místo `config`.

## 📊 Očekávané výsledky

Po úspešné migraci byste měli mít:
- **6 175 migravených článků** ve WordPress
- **55 600 migravených obrázků** ve WordPress
- **X galerií** (závisí na počtu článků s více obrázky)
- **Správné mapování** v souborech `mappings/*.json`

## 📝 Testování

Pro test migrace na malém vzorku:

```bash
# Test migrace galerií pro konkrétní článek
python migrate_galleries_mujdum.py 12345

# Test migrace s omezením
python migrate_galleries_mujdum.py --limit 10
