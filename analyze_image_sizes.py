#!/usr/bin/env python3
"""
Analýza velikostí obrázků pro identifikaci mal<PERSON> vs ve<PERSON><PERSON><PERSON> o<PERSON>r<PERSON><PERSON>
"""
import os
import re
import logging
from collections import defaultdict
from db_connectors import get_mysql_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA, WP_UPLOADS_PATH
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def analyze_image_sizes():
    """Analyzuje velikosti obrázků v migaci"""
    
    print("=" * 70)
    print("🔍 ANALÝZA VELIKOSTÍ OBRÁZKŮ")
    print("=" * 70)
    
    # 1. Analyzovat vzory názvů souborů
    print("\n1. ANALÝZA VZORŮ NÁZVŮ SOUBORŮ:")
    print("-" * 40)
    
    obrazek_dir = os.path.join(WP_UPLOADS_PATH, 'obrazek')
    size_patterns = defaultdict(list)
    total_files = 0
    
    # Projít všechny soubory v obrazek/
    for root, dirs, files in os.walk(obrazek_dir):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
                total_files += 1
                
                # Extrahovat rozměry z názvu
                size_match = re.search(r'_(\d+)x(\d+)', file)
                if size_match:
                    width, height = int(size_match.group(1)), int(size_match.group(2))
                    size_key = f"{width}x{height}"
                    size_patterns[size_key].append(file)
                else:
                    size_patterns['bez_rozmeru'].append(file)
    
    print(f"📊 Celkem {total_files} obrázků")
    print(f"📈 Rozdělení podle velikostí:")
    
    # Seřadit podle velikosti
    size_data = []
    for size_key, files in size_patterns.items():
        if size_key != 'bez_rozmeru':
            width, height = map(int, size_key.split('x'))
            size_data.append((width * height, size_key, len(files), files[:3]))
        else:
            size_data.append((999999, size_key, len(files), files[:3]))
    
    size_data.sort(reverse=True)
    
    for _, size_key, count, samples in size_data:
        print(f"   {size_key}: {count} souborů")
        for sample in samples:
            print(f"      📄 {sample}")
        if count > 3:
            print(f"         ... a dalších {count - 3}")
        print()
    
    # 2. Definovat kategorie velikostí
    print("2. KATEGORIZACE VELIKOSTÍ:")
    print("-" * 40)
    
    small_thumbnails = []
    medium_images = []
    large_images = []
    originals = []
    
    for pixel_count, size_key, count, samples in size_data:
        if size_key == 'bez_rozmeru':
            originals.extend([size_key, count])
        else:
            width, height = map(int, size_key.split('x'))
            if width <= 150 or height <= 150:
                small_thumbnails.append((size_key, count))
            elif width <= 400 or height <= 400:
                medium_images.append((size_key, count))
            else:
                large_images.append((size_key, count))
    
    print("🔸 Malé náhledy (≤150px):")
    for size, count in small_thumbnails[:10]:
        print(f"   {size}: {count} souborů")
    
    print("\n🔹 Střední obrázky (151-400px):")
    for size, count in medium_images[:10]:
        print(f"   {size}: {count} souborů")
    
    print("\n🔸 Velké obrázky (>400px):")
    for size, count in large_images[:10]:
        print(f"   {size}: {count} souborů")
    
    if originals:
        print(f"\n🔹 Originály (bez rozměrů): {originals[1]} souborů")
    
    # 3. Analyzovat mapování
    print("\n3. ANALÝZA MAPOVÁNÍ:")
    print("-" * 40)
    
    image_map = load_mapping('image_map.json')
    print(f"📊 Mapování obsahuje {len(image_map)} záznamů")
    
    # Kontrola velikostí v mapování
    mapped_small = 0
    mapped_medium = 0
    mapped_large = 0
    mapped_original = 0
    
    for path, info in image_map.items():
        if isinstance(info, dict) and 'wp_path' in info:
            filename = os.path.basename(info['wp_path'])
            size_match = re.search(r'_(\d+)x(\d+)', filename)
            if size_match:
                width, height = int(size_match.group(1)), int(size_match.group(2))
                if width <= 150 or height <= 150:
                    mapped_small += 1
                elif width <= 400 or height <= 400:
                    mapped_medium += 1
                else:
                    mapped_large += 1
            else:
                mapped_original += 1
    
    print(f"🗺️ V mapování:")
    print(f"   Malé náhledy: {mapped_small}")
    print(f"   Střední: {mapped_medium}")
    print(f"   Velké: {mapped_large}")
    print(f"   Originály: {mapped_original}")
    
    # 4. Kontrola galerií
    print("\n4. ANALÝZA GALERIÍ:")
    print("-" * 40)
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    # Najít články s galeriemi
    mysql_cursor.execute(f"""
        SELECT p.ID, p.post_title, p.post_content
        FROM {TBL_WP_POSTS} p
        JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'post' 
        AND pm.meta_key = 'sabre_database_version' 
        AND pm.meta_value = 'mujdum-2'
        AND p.post_content LIKE '%[gallery%'
        LIMIT 5
    """)
    
    galleries = mysql_cursor.fetchall()
    print(f"🖼️ Nalezeno {len(galleries)} článků s galeriemi (ukázka):")
    
    for post_id, title, content in galleries:
        gallery_matches = re.findall(r'\[gallery[^\]]*ids="([^"]+)"[^\]]*\]', content)
        if gallery_matches:
            ids = gallery_matches[0].split(',')
            print(f"\n   📖 {title[:40]}... (Post ID: {post_id})")
            print(f"      🔢 Galerie s {len(ids)} obrázky")
            
            # Kontrola velikostí obrázků v galerii
            for i, img_id in enumerate(ids[:3]):
                mysql_cursor.execute(f"""
                    SELECT guid FROM {TBL_WP_POSTS} WHERE ID = %s
                """, (img_id.strip(),))
                result = mysql_cursor.fetchone()
                if result:
                    url = result[0]
                    filename = os.path.basename(url)
                    size_match = re.search(r'_(\d+)x(\d+)', filename)
                    if size_match:
                        width, height = int(size_match.group(1)), int(size_match.group(2))
                        category = "malý" if (width <= 150 or height <= 150) else "střední" if (width <= 400 or height <= 400) else "velký"
                        print(f"         {i+1}. {width}x{height} ({category}): {filename[:50]}...")
                    else:
                        print(f"         {i+1}. originál: {filename[:50]}...")
            
            if len(ids) > 3:
                print(f"         ... a dalších {len(ids) - 3}")
    
    mysql_cursor.close()
    mysql_conn.close()
    
    # 5. Doporučení
    print("\n5. DOPORUČENÍ PRO OPRAVU:")
    print("-" * 40)
    print("🔧 Na základě analýzy:")
    print("   1. V galeriích používat pouze obrázky >400px nebo originály")
    print("   2. Pro featured images preferovat střední velikosti (151-400px)")
    print("   3. Malé náhledy (≤150px) nepoužívat v galeriích")
    print("   4. V obsahu článků preferovat velké obrázky")

if __name__ == "__main__":
    analyze_image_sizes()
