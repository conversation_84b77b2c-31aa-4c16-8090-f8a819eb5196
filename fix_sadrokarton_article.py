#!/usr/bin/env python3
"""
Analýza a automatická oprava článku "Sádrokarton v našich domovech"
"""
import os
import re
import logging
from bs4 import BeautifulSoup
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import TBL_CLANEK, TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_and_fix_sadrokarton():
    """Analyzuje a opravuje článek Sádrokarton"""
    
    print("=" * 70)
    print("🔍 ANALÝZA A OPRAVA ČLÁNKU: Sádrokarton v našich domovech")
    print("=" * 70)
    
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # 1. Najít článek v PostgreSQL
        print("\n1. PŮVODNÍ DATABÁZE (PostgreSQL):")
        print("-" * 40)
        
        pg_cursor.execute(f"""
            SELECT id_clanek, unikatni_id, nazev, text, obrazek_src, obrazek_alt
            FROM {TBL_CLANEK} 
            WHERE nazev ILIKE %s
        """, ("%Sádrokarton v našich domovech%",))
        
        pg_result = pg_cursor.fetchone()
        if not pg_result:
            print("❌ Článek nenalezen v PostgreSQL!")
            return False
            
        article_id, unique_id, title, original_text, featured_src, featured_alt = pg_result
        print(f"📖 Článek nalezen: ID {article_id}")
        print(f"   Název: {title}")
        print(f"   Unikátní ID: {unique_id}")
        print(f"   Featured image: {featured_src}")
        
        # Analyzovat původní text
        print(f"\n📝 Analýza původního textu:")
        if original_text:
            # Najít img tagy v původním textu
            img_tags = re.findall(r'<img[^>]*>', original_text)
            print(f"   Nalezeno {len(img_tags)} <img> tagů v původním textu")
            
            for i, img_tag in enumerate(img_tags[:3], 1):
                print(f"      {i}. {img_tag}")
            
            # Najít odkazy na obrázky
            image_urls = re.findall(r'https?://[^"\'\s]*(?:jpg|jpeg|png|gif)', original_text, re.IGNORECASE)
            print(f"   Nalezeno {len(image_urls)} URL obrázků:")
            for url in image_urls[:5]:
                print(f"      - {url}")
        
        # 2. Najít obrázky v tabulce obrazek
        print(f"\n2. OBRÁZKY V TABULCE OBRAZEK:")
        print("-" * 40)
        
        pg_cursor.execute(f"""
            SELECT id_obrazek, soubor, popisek, priorita, typ, active_state
            FROM {TBL_OBRAZEK} 
            WHERE polozka_id = %s AND active_state = 1
            ORDER BY priorita DESC, id_obrazek
        """, (unique_id,))
        
        obrazek_images = pg_cursor.fetchall()
        print(f"📊 Nalezeno {len(obrazek_images)} obrázků pro polozka_id {unique_id}:")
        
        featured_candidates = []
        gallery_images = []
        
        for img_id, filename, desc, priority, img_type, active in obrazek_images:
            print(f"   ID: {img_id} | Typ: {img_type} | Priorita: {priority} | {filename}")
            
            if img_type == 0:  # Featured image kandidát
                featured_candidates.append((img_id, filename, priority))
            elif img_type == 1:  # Galerie
                gallery_images.append((img_id, filename, priority))
        
        # 3. Zkontrolovat WordPress stav
        print(f"\n3. SOUČASNÝ STAV VE WORDPRESS:")
        print("-" * 40)
        
        article_map = load_mapping('article_map.json')
        if str(article_id) not in article_map:
            print("❌ Článek nebyl migrován do WordPress!")
            return False
            
        wp_post_id = article_map[str(article_id)]
        print(f"📖 WordPress Post ID: {wp_post_id}")
        
        # Zkontrolovat featured image
        mysql_cursor.execute(f"""
            SELECT pm.meta_value as thumbnail_id, att.guid
            FROM {TBL_WP_POSTMETA} pm
            LEFT JOIN {TBL_WP_POSTS} att ON pm.meta_value = att.ID
            WHERE pm.post_id = %s AND pm.meta_key = '_thumbnail_id'
        """, (wp_post_id,))
        
        featured_result = mysql_cursor.fetchone()
        current_featured_id = None
        current_featured_url = None
        
        if featured_result and featured_result[0]:
            current_featured_id = featured_result[0]
            current_featured_url = featured_result[1]
            print(f"🖼️ Současný featured image: ID {current_featured_id}")
            print(f"   URL: {current_featured_url}")
        else:
            print("❌ Featured image není nastaven!")
        
        # Zkontrolovat obsah
        mysql_cursor.execute(f"""
            SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s
        """, (wp_post_id,))
        
        wp_content = mysql_cursor.fetchone()[0]
        
        # Analyzovat WordPress obsah
        wp_img_tags = re.findall(r'<img[^>]*>', wp_content)
        print(f"📝 WordPress obsah obsahuje {len(wp_img_tags)} <img> tagů")
        
        if wp_img_tags:
            for i, img_tag in enumerate(wp_img_tags[:3], 1):
                print(f"   {i}. {img_tag[:100]}...")
        
        # 4. AUTOMATICKÉ OPRAVY
        print(f"\n4. AUTOMATICKÉ OPRAVY:")
        print("-" * 40)
        
        image_map = load_mapping('image_map.json')
        fixes_applied = 0
        
        # Oprava A: Nastavit správný featured image
        if not current_featured_id and featured_candidates:
            # Použít obrázek typu 0 s nejvyšší prioritou
            best_featured = max(featured_candidates, key=lambda x: x[2])  # podle priority
            featured_filename = best_featured[1]
            
            # Najít v mapování
            featured_wp_id = None
            for path, info in image_map.items():
                if isinstance(info, dict) and 'wp_id' in info:
                    if (os.path.basename(path) == featured_filename or 
                        path.endswith(f"/{featured_filename}") or
                        path.endswith(f"{unique_id}/{featured_filename}")):
                        featured_wp_id = info['wp_id']
                        break
            
            if featured_wp_id:
                mysql_cursor.execute(f"""
                    INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value)
                    VALUES (%s, '_thumbnail_id', %s)
                    ON DUPLICATE KEY UPDATE meta_value = %s
                """, (wp_post_id, featured_wp_id, featured_wp_id))
                
                print(f"✅ Nastaven featured image: {featured_filename} (ID: {featured_wp_id})")
                fixes_applied += 1
        
        # Oprava B: Opravit obrázky v obsahu
        # Zkontrolovat, zda img tagy mají správné URL
        problematic_images = 0
        for img_tag in wp_img_tags:
            if 'src="/obrazek/' in img_tag or 'src=\'/obrazek/' in img_tag:
                problematic_images += 1
        
        if problematic_images > 0:
            print(f"🔧 Nalezeno {problematic_images} obrázků s problematickými cestami, opravuji...")
            
            # Použít vylepšenou verzi update_image_urls_in_content
            fixed_content = fix_content_images_advanced(wp_content, image_map, unique_id)
            
            if fixed_content != wp_content:
                mysql_cursor.execute(f"""
                    UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
                """, (fixed_content, wp_post_id))
                
                # Spočítat kolik obrázků bylo opraveno
                fixed_img_tags = re.findall(r'<img[^>]*>', fixed_content)
                fixed_problematic = 0
                for img_tag in fixed_img_tags:
                    if 'src="/obrazek/' in img_tag or 'src=\'/obrazek/' in img_tag:
                        fixed_problematic += 1
                
                print(f"✅ Aktualizován obsah: opraveno {problematic_images - fixed_problematic} obrázků")
                fixes_applied += 1
        elif len(wp_img_tags) < len(img_tags):
            print(f"🔧 Chybí {len(img_tags) - len(wp_img_tags)} obrázků v obsahu, přidávám...")
            
            # Použít původní obsah pro přidání chybějících obrázků
            fixed_content = fix_content_images_advanced(original_text, image_map, unique_id)
            
            if fixed_content != wp_content:
                mysql_cursor.execute(f"""
                    UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
                """, (fixed_content, wp_post_id))
                
                new_img_count = len(re.findall(r'<img[^>]*>', fixed_content))
                print(f"✅ Aktualizován obsah: {new_img_count} obrázků (bylo {len(wp_img_tags)})")
                fixes_applied += 1
        
        # Oprava C: Přidat galerii pokud chybí
        if gallery_images and '[gallery' not in wp_content:
            gallery_ids = []
            for _, filename, _ in gallery_images:
                for path, info in image_map.items():
                    if isinstance(info, dict) and 'wp_id' in info:
                        if (os.path.basename(path) == filename or 
                            path.endswith(f"/{filename}") or
                            path.endswith(f"{unique_id}/{filename}")):
                            gallery_ids.append(str(info['wp_id']))
                            break
            
            if gallery_ids:
                gallery_shortcode = f'\n\n[gallery ids="{",".join(gallery_ids)}"]'
                new_content = wp_content + gallery_shortcode
                
                mysql_cursor.execute(f"""
                    UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s
                """, (new_content, wp_post_id))
                
                print(f"✅ Přidána galerie s {len(gallery_ids)} obrázky")
                fixes_applied += 1
        
        mysql_conn.commit()
        
        print(f"\n{'='*50}")
        print(f"✅ OPRAVA DOKONČENA: {fixes_applied} oprav aplikováno")
        print(f"{'='*50}")
        
        return fixes_applied > 0
        
    except Exception as e:
        mysql_conn.rollback()
        print(f"❌ Chyba: {e}")
        return False
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()

def fix_content_images_advanced(content, image_map, unique_id):
    """Pokročilá oprava obrázků v obsahu s důrazem na nalezení všech obrázků"""
    
    if not content:
        return content
    
    try:
        soup = BeautifulSoup(content, 'html.parser')
        images = soup.find_all('img')
        fixed_count = 0
        
        for img in images:
            if img.has_attr('src'):
                src = img['src']
                original_src = src
                
                # Pokusit se najít správné WordPress URL
                new_url = None
                
                # 1. Relativní cesta /obrazek/polozka_id/soubor.jpg
                if src.startswith('/obrazek/'):
                    # Extrahovat cestu bez úvodního /
                    path_part = src[1:]  # Odstranit úvodní /
                    print(f"   🔍 Hledám pro relativní cestu: {path_part}")
                    
                    # Hledat v mapování
                    for mapped_path, mapped_info in image_map.items():
                        if isinstance(mapped_info, dict) and 'wp_url' in mapped_info:
                            # Porovnat celou cestu
                            if mapped_path == path_part:
                                new_url = mapped_info['wp_url']
                                print(f"   ✅ Nalezeno přesné mapování: {mapped_path}")
                                break
                            # Porovnat podle názvu souboru v rámci polozka_id
                            elif path_part.startswith(f'{unique_id}/'):
                                filename = os.path.basename(path_part)
                                mapped_filename = os.path.basename(mapped_path)
                                if mapped_filename == filename and unique_id in mapped_path:
                                    new_url = mapped_info['wp_url']
                                    print(f"   ✅ Nalezeno podle názvu v polozka_id: {mapped_path}")
                                    break
                
                # 2. Pokud obsahuje mujdum.cz/obrazek/
                elif 'mujdum.cz/obrazek/' in src:
                    match = re.search(r'/obrazek/([^/]+/[^/\?]+)', src)
                    if match:
                        path_part = match.group(1)
                        print(f"   🔍 Hledám pro mujdum.cz cestu: {path_part}")
                        
                        # Hledat v mapování
                        for mapped_path, mapped_info in image_map.items():
                            if isinstance(mapped_info, dict) and 'wp_url' in mapped_info:
                                if (mapped_path == path_part or 
                                    os.path.basename(mapped_path) == os.path.basename(path_part)):
                                    new_url = mapped_info['wp_url']
                                    print(f"   ✅ Nalezeno mapování: {mapped_path}")
                                    break
                
                # 3. Fallback - hledat podle názvu souboru
                if not new_url:
                    filename = os.path.basename(src.split('?')[0])
                    print(f"   🔍 Fallback hledání podle názvu: {filename}")
                    
                    # Odstranit rozměry z názvu souboru pro lepší matching
                    # z "pysely-04_200x134.jpg" udělat "pysely-04.jpg"
                    base_filename = re.sub(r'_\d+x\d+', '', filename)
                    print(f"   🔍 Základní název bez rozměrů: {base_filename}")
                    
                    # Hledat podle základního názvu s polozka_id
                    for mapped_path, mapped_info in image_map.items():
                        if isinstance(mapped_info, dict) and 'wp_url' in mapped_info:
                            mapped_filename = os.path.basename(mapped_path)
                            
                            # Porovnat základní názvy (bez rozměrů)
                            if (mapped_filename == base_filename and unique_id in mapped_path):
                                new_url = mapped_info['wp_url']
                                print(f"   ✅ Nalezen podle základního názvu s polozka_id: {mapped_path}")
                                break
                            
                            # Nebo přesný název pokud existuje
                            elif (mapped_filename == filename and unique_id in mapped_path):
                                new_url = mapped_info['wp_url']
                                print(f"   ✅ Nalezen podle přesného názvu s polozka_id: {mapped_path}")
                                break
                    
                    # Úplný fallback - základní název bez polozka_id
                    if not new_url:
                        for mapped_path, mapped_info in image_map.items():
                            if isinstance(mapped_info, dict) and 'wp_url' in mapped_info:
                                mapped_filename = os.path.basename(mapped_path)
                                
                                if mapped_filename == base_filename:
                                    new_url = mapped_info['wp_url']
                                    print(f"   ⚠️ Fallback podle základního názvu: {mapped_path}")
                                    break
                                elif mapped_filename == filename:
                                    new_url = mapped_info['wp_url']
                                    print(f"   ⚠️ Fallback podle přesného názvu: {mapped_path}")
                                    break
                
                # Aktualizovat URL
                if new_url and new_url != src:
                    img['src'] = new_url
                    fixed_count += 1
                    print(f"   ✅ Opraven: {original_src} -> {new_url}")
                    logging.info(f"Opraven obrázek: {os.path.basename(original_src)} -> {new_url}")
                elif new_url == src:
                    print(f"   ⏭️ Již správné URL: {src}")
                else:
                    print(f"   ❌ Nenalezeno mapování pro: {src}")
        
        print(f"📊 Celkem opraveno {fixed_count} obrázků")
        return str(soup)
        
    except Exception as e:
        logging.error(f"Chyba při opravě obsahu: {e}")
        print(f"❌ Chyba při opravě obsahu: {e}")
        return content

def main():
    """Hlavní funkce - spustí analýzu a opravu"""
    
    print("🚀 Spouštím automatickou analýzu a opravu článku 'Sádrokarton v našich domovech'...")
    
    success = debug_and_fix_sadrokarton()
    
    if success:
        print("\n✅ Článek byl úspěšně opraven!")
        print("🎯 Zkontrolujte článek na webu")
    else:
        print("\n❌ Oprava se nezdařila nebo nebyla potřeba")

if __name__ == "__main__":
    main()
