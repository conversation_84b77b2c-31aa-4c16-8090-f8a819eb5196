import os
from dotenv import load_dotenv

load_dotenv()

# PostgreSQL Config
PG_CONFIG = {
    'database': os.getenv('PG_DBNAME'),
    'user': os.getenv('PG_USER'),
    'password': os.getenv('PG_PASSWORD'),
    'host': os.getenv('PG_HOST'),
    'port': os.getenv('PG_PORT', 5432)
}

# MySQL Config
MYSQL_CONFIG = {
    'database': os.getenv('MYSQL_DBNAME'),
    'user': os.getenv('MYSQL_USER'),
    'password': os.getenv('MYSQL_PASSWORD'),
    'host': os.getenv('MYSQL_HOST'),
    'port': os.getenv('MYSQL_PORT', 3306)
}

# Paths and URLs
WP_UPLOADS_PATH = os.getenv('WP_UPLOADS_PATH')
WP_SITE_URL = os.getenv('WP_SITE_URL', '').rstrip('/')
OLD_IMAGE_BASE_PATH = os.getenv('OLD_IMAGE_BASE_PATH')

# Other settings
DEFAULT_WP_USER_ID = int(os.getenv('DEFAULT_WP_USER_ID', 1))
SABRE_TABLE_PREFIX = os.getenv('SABRE_TABLE_PREFIX', '')
WP_TABLE_PREFIX = os.getenv('WP_TABLE_PREFIX', 'wp_')

# --- SABRE Tables (AKTUALIZOVANÉ PRO MUJDUM) ---
TBL_CLANEK = f"{SABRE_TABLE_PREFIX}clanek"
TBL_OBRAZEK = f"{SABRE_TABLE_PREFIX}obrazek"
TBL_RUBRIKA = f"{SABRE_TABLE_PREFIX}rubrika"

# POZOR: V nové databázi mujdum-2 se místo prefix_galerie a prefix_galerie_polozka používá:
# - prefix_fotogalerie (ale je prázdná)
# - Galerie se řeší přes prefix_obrazek s polozka_id + nový sloupec nazev_galerie v prefix_clanek
TBL_FOTOGALERIE = f"{SABRE_TABLE_PREFIX}fotogalerie"

# Původní tabulky galerií (nejsou v nové DB):
# TBL_GALERIE = f"{SABRE_TABLE_PREFIX}galerie"  
# TBL_GALERIE_POLOZKA = f"{SABRE_TABLE_PREFIX}galerie_polozka"

# --- WordPress Tables ---
TBL_WP_POSTS = f"{WP_TABLE_PREFIX}posts"
TBL_WP_POSTMETA = f"{WP_TABLE_PREFIX}postmeta"
TBL_WP_TERMS = f"{WP_TABLE_PREFIX}terms"
TBL_WP_TERM_TAXONOMY = f"{WP_TABLE_PREFIX}term_taxonomy"
TBL_WP_TERM_RELATIONSHIPS = f"{WP_TABLE_PREFIX}term_relationships"
TBL_WP_USERS = f"{WP_TABLE_PREFIX}users"

# Mapping files directory
MAPPINGS_DIR = 'mappings'
os.makedirs(MAPPINGS_DIR, exist_ok=True)

# Validate essential config
if not all([PG_CONFIG['database'], PG_CONFIG['user'], MYSQL_CONFIG['database'], MYSQL_CONFIG['user'], WP_UPLOADS_PATH, OLD_IMAGE_BASE_PATH, WP_SITE_URL]):
    raise ValueError("Zkontrolujte konfiguraci v .env - chybí důležité hodnoty!")
