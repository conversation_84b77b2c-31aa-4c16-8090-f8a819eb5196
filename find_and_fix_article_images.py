#!/usr/bin/env python3
"""
Najde a opraví malé náhledy konkrétního článku nahrazením za originá<PERSON> script pro článek: "Přeměňte svou zahradu na stylový relaxační koutek"
"""
import json
import re
import os
import logging
from db_connectors import get_mysql_connection, get_pg_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA, TBL_CLANEK, TBL_OBRAZEK
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class OriginalImageFinder:
    def __init__(self, image_map):
        self.image_map = image_map
        self.original_cache = {}
        self._build_original_cache()
    
    def _build_original_cache(self):
        """Postaví cache originálních obrázků podle základních názvů"""
        logging.info("Buduje cache originálních obrázků...")
        
        for path, info in self.image_map.items():
            if not isinstance(info, dict) or 'wp_url' not in info:
                continue
                
            filename = os.path.basename(info['wp_url'])
            
            # Rozlišit originály (bez rozměrů) od náhledů (s rozměry)
            if not re.search(r'_\d+x\d+', filename):
                # Je to originál
                base_name = self.get_base_filename(filename)
                article_id = self.extract_article_id(path)
                
                key = f"{article_id}/{base_name}"
                if key not in self.original_cache:
                    self.original_cache[key] = []
                
                self.original_cache[key].append({
                    'wp_id': info['wp_id'],
                    'wp_url': info['wp_url'],
                    'path': path,
                    'filename': filename
                })
        
        logging.info(f"Cache postavena pro {len(self.original_cache)} základních názvů")
    
    def get_base_filename(self, filename):
        """Získá základní název souboru bez hash a rozměrů"""
        # Odstranit rozměry: file_800x600.jpg -> file.jpg
        base = re.sub(r'_\d+x\d+', '', filename)
        # Odstranit hash: file-abc123def.jpg -> file.jpg  
        base = re.sub(r'-[a-f0-9]{10,}', '', base)
        return base
    
    def extract_article_id(self, path):
        """Extrahuje ID článku z cesty"""
        match = re.search(r'([a-f0-9]{10,})', path)
        return match.group(1) if match else None
    
    def find_original_for_thumbnail(self, thumbnail_path, thumbnail_info):
        """Najde originál pro daný thumbnail"""
        article_id = self.extract_article_id(thumbnail_path)
        if not article_id:
            return None
            
        thumbnail_filename = os.path.basename(thumbnail_info['wp_url'])
        base_name = self.get_base_filename(thumbnail_filename)
        
        # Hledat originál se stejným základním názvem ve stejném článku
        key = f"{article_id}/{base_name}"
        
        if key in self.original_cache:
            # Vrátit první (a pravděpodobně jediný) originál
            originals = self.original_cache[key]
            if originals:
                return originals[0]
        
        return None

def find_article_in_mapping(article_unique_id, image_map):
    """Najde všechny obrázky konkrétního článku v mapování"""
    article_images = {}
    
    for path, info in image_map.items():
        if not isinstance(info, dict):
            continue
            
        if article_unique_id in path:
            filename = os.path.basename(info.get('wp_url', ''))
            article_images[path] = {
                'wp_id': info.get('wp_id'),
                'wp_url': info.get('wp_url'),
                'filename': filename,
                'is_thumbnail': bool(re.search(r'_\d+x\d+', filename))
            }
    
    return article_images

def fix_article_images(article_unique_id, mysql_cursor, image_map, finder):
    """Opraví obrázky konkrétního článku"""
    
    print(f"\n🔧 OPRAVUJI ČLÁNEK: {article_unique_id}")
    print("=" * 60)
    
    # Najít článek v mapování
    article_images = find_article_in_mapping(article_unique_id, image_map)
    
    print(f"📊 Nalezeno {len(article_images)} obrázků v mapování")
    
    replacements = []
    
    # Projít každý obrázek a najít lepší verzi
    for path, img_info in article_images.items():
        if img_info['is_thumbnail']:
            print(f"\n🔍 Kontroluji thumbnail: {img_info['filename']}")
            
            # Najít originál
            original = finder.find_original_for_thumbnail(path, img_info)
            
            if original:
                print(f"   ✅ Nalezen originál: {original['filename']}")
                print(f"   📝 Nahradím ID {img_info['wp_id']} -> {original['wp_id']}")
                
                replacements.append({
                    'old_id': img_info['wp_id'],
                    'new_id': original['wp_id'],
                    'old_url': img_info['wp_url'],
                    'new_url': original['wp_url'],
                    'old_filename': img_info['filename'],
                    'new_filename': original['filename']
                })
            else:
                print(f"   ❌ Originál nenalezen pro: {img_info['filename']}")
    
    if not replacements:
        print("\n❌ Žádné náhrady nebyly nalezeny")
        return False
    
    print(f"\n🎯 CELKEM NÁHRAD: {len(replacements)}")
    
    # Najít WordPress post pro tento článek
    mysql_cursor.execute(f"""
        SELECT p.ID, p.post_title, p.post_content, pm.meta_value as thumbnail_id
        FROM {TBL_WP_POSTS} p
        JOIN {TBL_WP_POSTMETA} pm_version ON p.ID = pm_version.post_id
        LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
        WHERE p.post_type = 'post' 
        AND pm_version.meta_key = 'sabre_database_version' 
        AND pm_version.meta_value = 'mujdum-2'
        AND p.post_content LIKE %s
    """, (f"%{article_unique_id}%",))
    
    post_result = mysql_cursor.fetchone()
    
    if not post_result:
        print("❌ WordPress post nenalezen")
        return False
        
    post_id, post_title, post_content, current_thumbnail = post_result
    print(f"\n📖 WordPress post: {post_id} - {post_title}")
    
    # Aplikovat náhrady
    updated_content = post_content
    featured_image_updated = False
    
    for replacement in replacements:
        old_id = replacement['old_id']
        new_id = replacement['new_id']
        
        # 1. Nahradit v galeriích
        updated_content = re.sub(
            rf'\b{old_id}\b',
            str(new_id),
            updated_content
        )
        
        # 2. Nahradit featured image pokud se shoduje
        if current_thumbnail and int(current_thumbnail) == old_id:
            mysql_cursor.execute(f"""
                UPDATE {TBL_WP_POSTMETA} 
                SET meta_value = %s 
                WHERE post_id = %s AND meta_key = '_thumbnail_id'
            """, (new_id, post_id))
            featured_image_updated = True
            print(f"   🎯 Featured image aktualizován: {old_id} -> {new_id}")
    
    # Uložit aktualizovaný obsah
    mysql_cursor.execute(f"""
        UPDATE {TBL_WP_POSTS} 
        SET post_content = %s 
        WHERE ID = %s
    """, (updated_content, post_id))
    
    print(f"\n✅ OPRAVA DOKONČENA:")
    print(f"   📝 Obsah aktualizován: {len(replacements)} náhrad")
    if featured_image_updated:
        print(f"   🎯 Featured image aktualizován")
    
    return True

def main():
    """Hlavní funkce - test na konkrétním článku"""
    
    # ID článku z debug výstupu
    article_unique_id = "665510acb770a"
    article_title = "Přeměňte svou zahradu na stylový relaxační koutek"
    
    print("=" * 80)
    print(f"🔧 OPRAVA OBRÁZKŮ KONKRÉTNÍHO ČLÁNKU")
    print("=" * 80)
    print(f"📖 Článek: {article_title}")
    print(f"🆔 Unique ID: {article_unique_id}")
    print()
    
    # Načíst mapování
    logging.info("Načítám mapování obrázků...")
    image_map = load_mapping('image_map.json')
    logging.info(f"Načteno {len(image_map)} mapování obrázků")
    
    # Připojit k databázi
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Inicializovat finder
        finder = OriginalImageFinder(image_map)
        
        # Opravit článek
        success = fix_article_images(article_unique_id, mysql_cursor, image_map, finder)
        
        if success:
            mysql_conn.commit()
            print(f"\n🎉 OPRAVA ÚSPĚŠNĚ DOKONČENA!")
            print(f"🔍 Doporučuji nyní spustit: python debug_specific_article.py \"{article_title}\"")
        else:
            print(f"\n❌ OPRAVA SE NEZDAŘILA")
            
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Chyba: {e}")
        print(f"\n💥 CHYBA: {e}")
    finally:
        mysql_cursor.close()
        mysql_conn.close()

if __name__ == "__main__":
    main()
