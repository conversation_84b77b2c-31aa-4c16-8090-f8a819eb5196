#!/usr/bin/env python3
"""
Debug skript pro konkrétní <PERSON> - analýza featured images a galerií
"""
import logging
import os
from db_connectors import get_pg_connection, get_mysql_connection
from config_mujdum import TBL_CLANEK, TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_specific_article(article_title="Radost uklízet!"):
    """Analyzuje konkrétní článek pro zjištění problémů"""
    
    print("=" * 70)
    print(f"🔍 DEBUG KONKRÉTNÍHO ČLÁNKU: {article_title}")
    print("=" * 70)
    
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # 1. Najít článek v původní databázi
        print("\n1. PŮVODNÍ DATABÁZE:")
        print("-" * 40)
        
        pg_cursor.execute(f"""
            SELECT id_clanek, unikatni_id, nazev, obrazek_src, obrazek_alt, nazev_galerie
            FROM {TBL_CLANEK} 
            WHERE nazev ILIKE %s
        """, (f"%{article_title}%",))
        
        articles = pg_cursor.fetchall()
        if not articles:
            print(f"❌ Článek '{article_title}' nebyl nalezen!")
            return
        
        for article_id, unique_id, title, featured_src, featured_alt, gallery_name in articles:
            print(f"📖 Článek ID: {article_id}")
            print(f"   Název: {title}")
            print(f"   Unikátní ID: {unique_id}")
            print(f"   Featured image: {featured_src}")
            print(f"   Featured alt: {featured_alt}")
            print(f"   Název galerie: {gallery_name}")
            print()
            
            # 2. Najít obrázky pro tento článek
            print("2. OBRÁZKY V PŮVODNÍ DATABÁZI:")
            print("-" * 40)
            
            pg_cursor.execute(f"""
                SELECT id_obrazek, soubor, popisek, priorita, typ, active_state
                FROM {TBL_OBRAZEK} 
                WHERE polozka_id = %s
                ORDER BY priorita DESC, id_obrazek
            """, (unique_id,))
            
            images = pg_cursor.fetchall()
            print(f"📊 Celkem {len(images)} obrázků pro článek:")
            
            for img_id, filename, desc, priority, img_type, active in images:
                status = "✅" if active == 1 else "❌"
                print(f"   {status} ID: {img_id} | Typ: {img_type} | Priorita: {priority}")
                print(f"      Soubor: {filename}")
                print(f"      Popis: {desc}")
                print()
            
            # 3. Zkontrolovat mapování
            print("3. MAPOVÁNÍ OBRÁZKŮ:")
            print("-" * 40)
            
            image_map = load_mapping('image_map.json')
            
            print(f"🗺️ Hledání featured image '{featured_src}' v mapování:")
            if featured_src in image_map:
                print(f"   ✅ Nalezen: {image_map[featured_src]}")
            else:
                print(f"   ❌ Nenalezen přímým mapováním")
                # Zkusit podle názvu souboru
                featured_filename = os.path.basename(featured_src) if featured_src else None
                found = False
                if featured_filename:
                    for path, info in image_map.items():
                        if os.path.basename(path) == featured_filename:
                            print(f"   🔍 Nalezen podle názvu souboru: {path} -> {info}")
                            found = True
                            break
                if not found:
                    print(f"   ❌ Nenalezen ani podle názvu souboru: {featured_filename}")
            
            print(f"\n🗺️ Mapování obrázků galerie:")
            for img_id, filename, desc, priority, img_type, active in images:
                if active == 1:
                    print(f"   Hledám: {filename}")
                    if filename in image_map:
                        print(f"      ✅ Nalezen: {image_map[filename]}")
                    else:
                        # Zkusit s polozka_id
                        full_path = f"{unique_id}/{filename}"
                        if full_path in image_map:
                            print(f"      ✅ Nalezen s polozka_id: {full_path} -> {image_map[full_path]}")
                        else:
                            print(f"      ❌ Nenalezen: {filename}")
            
            # 4. Zkontrolovat WordPress
            print("\n4. WORDPRESS MIGRACE:")
            print("-" * 40)
            
            article_map = load_mapping('article_map.json')
            if str(article_id) in article_map:
                wp_post_id = article_map[str(article_id)]
                print(f"📖 WordPress Post ID: {wp_post_id}")
                
                # Featured image
                mysql_cursor.execute(f"""
                    SELECT pm.meta_value as thumbnail_id, att.guid
                    FROM {TBL_WP_POSTMETA} pm
                    LEFT JOIN {TBL_WP_POSTS} att ON pm.meta_value = att.ID
                    WHERE pm.post_id = %s AND pm.meta_key = '_thumbnail_id'
                """, (wp_post_id,))
                
                featured_result = mysql_cursor.fetchone()
                if featured_result:
                    thumb_id, thumb_url = featured_result
                    print(f"🖼️ Featured image: ID {thumb_id}")
                    print(f"   URL: {thumb_url}")
                else:
                    print("❌ Featured image nenastaven!")
                
                # Obsah s galerií
                mysql_cursor.execute(f"""
                    SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s
                """, (wp_post_id,))
                
                content = mysql_cursor.fetchone()[0]
                if '[gallery' in content:
                    import re
                    gallery_matches = re.findall(r'\[gallery[^\]]*\]', content)
                    print(f"\n🖼️ Galerie v obsahu:")
                    for i, gallery in enumerate(gallery_matches, 1):
                        print(f"   {i}. {gallery}")
                        
                        # Extrahovat IDs
                        ids_match = re.search(r'ids="([^"]+)"', gallery)
                        if ids_match:
                            ids = ids_match.group(1).split(',')
                            print(f"      IDs: {len(ids)} obrázků")
                            for j, img_id in enumerate(ids[:3], 1):
                                mysql_cursor.execute(f"""
                                    SELECT guid FROM {TBL_WP_POSTS} WHERE ID = %s
                                """, (img_id.strip(),))
                                url_result = mysql_cursor.fetchone()
                                url = url_result[0] if url_result else "NENALEZENO"
                                print(f"         {j}. ID {img_id}: {url}")
                            if len(ids) > 3:
                                print(f"         ... a dalších {len(ids) - 3}")
                else:
                    print("❌ Žádná galerie v obsahu!")
                    
            else:
                print(f"❌ Článek ID {article_id} nebyl migrován do WordPress!")
    
    except Exception as e:
        print(f"❌ Chyba: {e}")
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()

if __name__ == "__main__":
    import sys
    title = sys.argv[1] if len(sys.argv) > 1 else "Radost uklízet!"
    debug_specific_article(title)
