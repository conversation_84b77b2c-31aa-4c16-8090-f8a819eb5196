#!/usr/bin/env python3
"""
Rychlá kontrola konkrétního článku
"""
import mysql.connector
from db_connectors import get_mysql_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA

def quick_check():
    print("🔍 RYCHLÁ KONTROLA ČLÁNKU")
    print("=" * 50)
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Najít článek
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, pm.meta_value as polozka_id
            FROM {TBL_WP_POSTS} p
            LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = 'sabre_unikatni_id'
            WHERE p.post_type = 'post' 
            AND p.post_title LIKE %s
        """, ("%Nemáte zahradu%",))
        
        articles = mysql_cursor.fetchall()
        print(f"Nalezeno {len(articles)} článků:")
        
        for post_id, title, polozka_id in articles:
            print(f"\nID: {post_id}")
            print(f"Název: {title}")
            print(f"Polozka ID: {polozka_id}")
            
            if polozka_id:
                # Najít obrázky s tímto polozka_id
                mysql_cursor.execute(f"""
                    SELECT COUNT(*) 
                    FROM {TBL_WP_POSTS} p
                    JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id
                    WHERE p.post_type = 'attachment'
                    AND pm.meta_key = 'sabre_polozka_id'
                    AND pm.meta_value = %s
                """, (polozka_id,))
                
                image_count = mysql_cursor.fetchone()[0]
                print(f"Obrázků pro tento článek: {image_count}")
                
                # Featured image
                mysql_cursor.execute(f"""
                    SELECT pm.meta_value as thumb_id
                    FROM {TBL_WP_POSTMETA} pm
                    WHERE pm.post_id = %s AND pm.meta_key = '_thumbnail_id'
                """, (post_id,))
                
                featured = mysql_cursor.fetchone()
                if featured:
                    thumb_id = featured[0]
                    print(f"Featured image ID: {thumb_id}")
                    
                    # Zkontrolovat polozka_id featured image
                    mysql_cursor.execute(f"""
                        SELECT pm.meta_value, p.guid
                        FROM {TBL_WP_POSTMETA} pm
                        JOIN {TBL_WP_POSTS} p ON pm.post_id = p.ID
                        WHERE pm.post_id = %s AND pm.meta_key = 'sabre_polozka_id'
                    """, (thumb_id,))
                    
                    featured_info = mysql_cursor.fetchone()
                    if featured_info:
                        featured_polozka, featured_url = featured_info
                        print(f"Featured polozka_id: {featured_polozka}")
                        print(f"Featured URL: {featured_url}")
                        
                        if featured_polozka != polozka_id:
                            print("⚠️  PROBLÉM: Featured image z jiného článku!")
                        else:
                            print("✅ Featured image OK")
                else:
                    print("❌ Žádný featured image")
            
            break  # Jen první článek
    
    except Exception as e:
        print(f"Chyba: {e}")
    
    finally:
        mysql_cursor.close()
        mysql_conn.close()

if __name__ == "__main__":
    quick_check()
