#!/usr/bin/env python3
"""
Validační skript pro kontrolu image_map.json a detekci potenciálních problémů
"""
import json
import os
import re
from collections import defaultdict, Counter
from db_connectors import get_mysql_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA

def analyze_image_mapping():
    """Analyzuje image_map.json a detekuje potenciální problémy"""
    
    print("🔍 ANALÝZA IMAGE MAPPING")
    print("=" * 50)
    
    # Načíst image_map.json
    try:
        with open('mappings/image_map.json', 'r') as f:
            image_map = json.load(f)
        print(f"✓ Načteno {len(image_map)} záznamů z image_map.json")
    except Exception as e:
        print(f"✗ Chyba při načítání image_map.json: {e}")
        return False
    
    # Analýza struktury dat
    polozka_stats = defaultdict(list)
    filename_stats = defaultdict(list)
    size_stats = defaultdict(int)
    
    for path, info in image_map.items():
        if isinstance(info, dict) and 'wp_url' in info:
            # Extrahovat polozka_id z cesty
            polozka_id = path.split('/')[0] if '/' in path else 'unknown'
            polozka_stats[polozka_id].append(path)
            
            # Analýza názvů souborů
            filename = os.path.basename(path)
            base_name = re.sub(r'_\d+x\d+', '', filename)  # Odstranit rozměry
            base_name = re.sub(r'-[a-f0-9]{10,}', '', base_name)  # Odstranit hash
            filename_stats[base_name].append(path)
            
            # Analýza velikostí
            match = re.search(r'_(\d+)x(\d+)', filename)
            if match:
                size = f"{match.group(1)}x{match.group(2)}"
                size_stats[size] += 1
    
    print(f"\n📊 STATISTIKY:")
    print(f"   Celkem článků (polozka_id): {len(polozka_stats)}")
    print(f"   Celkem unikátních názvů: {len(filename_stats)}")
    print(f"   Celkem velikostí: {len(size_stats)}")
    
    # Najít články s nejvíce obrázky
    print(f"\n🏆 TOP 10 ČLÁNKŮ S NEJVÍCE OBRÁZKY:")
    top_articles = sorted(polozka_stats.items(), key=lambda x: len(x[1]), reverse=True)[:10]
    for polozka_id, paths in top_articles:
        print(f"   {polozka_id}: {len(paths)} obrázků")
    
    # Najít duplicitní názvy souborů
    print(f"\n⚠️  POTENCIÁLNÍ PROBLÉMY:")
    duplicates = {name: paths for name, paths in filename_stats.items() if len(paths) > 1}
    if duplicates:
        print(f"   Nalezeno {len(duplicates)} duplicitních názvů souborů:")
        for name, paths in list(duplicates.items())[:5]:  # Ukázat jen prvních 5
            articles = set(path.split('/')[0] for path in paths)
            print(f"     '{name}': {len(paths)} souborů v {len(articles)} článcích")
            if len(articles) > 1:
                print(f"       ⚠️  CROSS-ARTICLE: {list(articles)[:3]}")
    else:
        print("   ✓ Žádné duplicitní názvy souborů")
    
    # Analýza velikostí
    print(f"\n📏 NEJČASTĚJŠÍ VELIKOSTI:")
    top_sizes = sorted(size_stats.items(), key=lambda x: x[1], reverse=True)[:10]
    for size, count in top_sizes:
        print(f"   {size}: {count} obrázků")
    
    return True, polozka_stats, duplicates

def check_database_consistency():
    """Zkontroluje konzistenci mezi image_map.json a databází"""
    
    print(f"\n🔗 KONTROLA KONZISTENCE S DATABÁZÍ")
    print("=" * 50)
    
    try:
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor()
        
        # Načíst image_map.json
        with open('mappings/image_map.json', 'r') as f:
            image_map = json.load(f)
        
        # Zkontrolovat, kolik obrázků má polozka_id v databázi
        mysql_cursor.execute(f"""
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN pm.meta_value IS NOT NULL AND pm.meta_value != '' THEN 1 END) as with_polozka_id
            FROM {TBL_WP_POSTS} p
            LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = 'sabre_polozka_id'
            WHERE p.post_type = 'attachment'
        """)
        
        total, with_polozka_id = mysql_cursor.fetchone()
        print(f"   Celkem attachmentů v DB: {total}")
        print(f"   S polozka_id: {with_polozka_id}")
        print(f"   Bez polozka_id: {total - with_polozka_id}")
        
        if with_polozka_id < total * 0.8:  # Méně než 80% má polozka_id
            print(f"   ⚠️  VAROVÁNÍ: Pouze {with_polozka_id/total*100:.1f}% obrázků má polozka_id")
        else:
            print(f"   ✓ Dobré pokrytí polozka_id: {with_polozka_id/total*100:.1f}%")
        
        # Zkontrolovat, kolik obrázků z image_map existuje v DB
        wp_ids_in_map = [info['wp_id'] for info in image_map.values() if isinstance(info, dict) and 'wp_id' in info]
        
        if wp_ids_in_map:
            placeholders = ','.join(['%s'] * len(wp_ids_in_map[:1000]))  # Limit pro test
            mysql_cursor.execute(f"""
                SELECT COUNT(*) FROM {TBL_WP_POSTS} 
                WHERE ID IN ({placeholders}) AND post_type = 'attachment'
            """, wp_ids_in_map[:1000])
            
            existing_count = mysql_cursor.fetchone()[0]
            tested_count = min(len(wp_ids_in_map), 1000)
            print(f"   Testováno {tested_count} ID z image_map: {existing_count} existuje v DB")
            
            if existing_count < tested_count * 0.95:  # Méně než 95% existuje
                print(f"   ⚠️  VAROVÁNÍ: Některé obrázky z image_map neexistují v DB")
            else:
                print(f"   ✓ Dobrá konzistence: {existing_count/tested_count*100:.1f}%")
        
        mysql_cursor.close()
        mysql_conn.close()
        
        return True
        
    except Exception as e:
        print(f"   ✗ Chyba při kontrole databáze: {e}")
        return False

def recommend_test_articles(polozka_stats):
    """Doporučí články pro testování"""
    
    print(f"\n🎯 DOPORUČENÉ ČLÁNKY PRO TESTOVÁNÍ:")
    print("=" * 50)
    
    # Články s různým počtem obrázků
    sorted_articles = sorted(polozka_stats.items(), key=lambda x: len(x[1]), reverse=True)
    
    print("   Testovací sada:")
    print(f"   1. Článek s nejvíce obrázky: {sorted_articles[0][0]} ({len(sorted_articles[0][1])} obrázků)")
    
    if len(sorted_articles) > len(sorted_articles)//2:
        mid_idx = len(sorted_articles)//2
        print(f"   2. Střední článek: {sorted_articles[mid_idx][0]} ({len(sorted_articles[mid_idx][1])} obrázků)")
    
    if len(sorted_articles) > 10:
        print(f"   3. Malý článek: {sorted_articles[-10][0]} ({len(sorted_articles[-10][1])} obrázků)")
    
    # Najít články s potenciálně problematickými názvy
    problem_articles = set()
    for paths in polozka_stats.values():
        filenames = [os.path.basename(p) for p in paths]
        base_names = [re.sub(r'_\d+x\d+', '', f) for f in filenames]
        if len(set(base_names)) < len(base_names):  # Duplicitní base názvy
            problem_articles.add(paths[0].split('/')[0])
    
    if problem_articles:
        print(f"   4. Problematické články (duplicitní názvy): {list(problem_articles)[:3]}")
    
    return sorted_articles[:3]

if __name__ == "__main__":
    success, polozka_stats, duplicates = analyze_image_mapping()
    
    if success:
        check_database_consistency()
        recommend_test_articles(polozka_stats)
        
        print(f"\n✅ ANALÝZA DOKONČENA")
        print(f"   Spusťte tento skript před použitím fix_final_mujdum_issues_fast.py")
        print(f"   Zkontrolujte varování výše a případně opravte problémy")
