#!/usr/bin/env python3
"""
Postupná migrace - testování na malé sadě před celkovou migrací
"""
import os
import sys
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def run_test_migration():
    """Spustí testovací migraci na prvních 100 článcích"""
    print("🧪 TESTOVACÍ MIGRACE")
    print("=" * 50)
    print("Migruje pouze prvních 100 článků pro testování")
    print()
    
    # Import s omezenými parametry
    from migrate_images_mujdum_fixed import migrate_images_fixed
    from migrate_articles_mujdum_fixed import migrate_articles_fixed
    
    try:
        # 1. Migrace obrázků (omezeno)
        print("🖼️  Migrace obrázků (prvních 1000)...")
        migrate_images_fixed(batch_size=100, start_offset=0)  # Jen 10 dávek
        
        # 2. <PERSON><PERSON><PERSON> čl<PERSON>ů (omezeno)  
        print("\n📝 Migrace článků (prvních 100)...")
        migrate_articles_fixed(batch_size=50, start_offset=0)  # Jen 2 dávky
        
        print("\n✅ TESTOVACÍ MIGRACE DOKONČENA")
        print("🌐 Zkontrolujte: http://mujdum.test")
        
    except Exception as e:
        print(f"❌ Chyba v testovací migraci: {e}")

def run_full_migration():
    """Spustí celkovou migraci"""
    print("🚀 CELKOVÁ MIGRACE")
    print("=" * 50)
    
    from migrate_images_mujdum_fixed import migrate_images_fixed
    from migrate_articles_mujdum_fixed import migrate_articles_fixed
    from migrate_galleries_mujdum import migrate_galleries
    
    try:
        # 1. Migrace všech obrázků
        print("🖼️  Migrace všech obrázků...")
        migrate_images_fixed()
        
        # 2. Migrace všech článků
        print("\n📝 Migrace všech článků...")
        migrate_articles_fixed()
        
        # 3. Migrace galerií
        print("\n🖼️  Migrace galerií...")
        migrate_galleries()
        
        print("\n🎉 CELKOVÁ MIGRACE DOKONČENA")
        
    except Exception as e:
        print(f"❌ Chyba v celkové migraci: {e}")

def main():
    print("🔧 POSTUPNÁ MIGRACE")
    print("=" * 60)
    print("Vyberte režim migrace:")
    print()
    print("1. Testovací migrace (100 článků)")
    print("2. Celková migrace (všechny články)")
    print("3. Pouze dokončit opravu (doporučeno)")
    print()
    
    choice = input("Vyberte možnost (1-3): ").strip()
    
    if choice == "1":
        run_test_migration()
    elif choice == "2":
        confirm = input("⚠️  Opravdu chcete migrovat VŠECHNA data? (ano/ne): ").lower()
        if confirm in ['ano', 'yes', 'y']:
            run_full_migration()
        else:
            print("❌ Migrace zrušena")
    elif choice == "3":
        print("✅ Oprava už byla dokončena!")
        print("🌐 Zkontrolujte výsledky na: http://mujdum.test")
        print("📊 Statistiky:")
        print("   - 267,987 obrázků analyzováno")
        print("   - 39,328 záznamů v mapování (59.8% kvalitních)")
        print("   - 2,643 featured images opraveno (41.3%)")
    else:
        print("❌ Neplatná volba")

if __name__ == "__main__":
    main()
