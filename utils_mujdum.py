import json
import os
import re
from slugify import slugify as pyslugify
from config_mujdum import MAPPINGS_DIR
import logging
from datetime import datetime
import shutil
import mimetypes
from bs4 import BeautifulSoup

def load_mapping(filename):
    """Načte mapování ID ze souboru JSON."""
    filepath = os.path.join(MAPPINGS_DIR, filename)
    if os.path.exists(filepath):
        try:
            with open(filepath, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            logging.warning(f"Soubor mapování {filename} je poškozený nebo prázdný.")
            return {}
    return {}

def save_mapping(data, filename):
    """Uloží mapování ID do souboru JSON."""
    filepath = os.path.join(MAPPINGS_DIR, filename)
    try:
        # Zkontrolovat velikost dat
        data_size = len(str(data))
        if data_size > 10 * 1024 * 1024:  # 10 MB limit
            logging.warning(f"Velikost dat pro {filename} je {data_size / (1024*1024):.2f} MB, což může způsobit problémy.")
        
        with open(filepath, 'w') as f:
            json.dump(data, f)  # Odstranění indentace pro úsporu místa
        
        logging.info(f"Mapování uloženo do {filename} ({len(data)} záznamů)")
        return True
    except Exception as e:
        logging.error(f"Chyba při ukládání mapování do {filename}: {e}")
        return False

def generate_slug(text):
    """Vygeneruje URL-friendly slug."""
    if not text:
        return f"post-{datetime.now().strftime('%Y%m%d%H%M%S%f')}"  # Fallback pro prázdný titulek
    return pyslugify(text, max_length=200)  # max_length for WP posts table

def format_wp_datetime(dt_obj):
    """Formátuje datetime objekt pro WordPress (YYYY-MM-DD HH:MM:SS)."""
    if dt_obj is None:
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Zajistit, že dt_obj je datetime objekt
    if not isinstance(dt_obj, datetime):
        # Zkuste převést, pokud je to řetězec nebo jiný typ
        try:
            # Zkuste běžné formáty
            dt_obj = datetime.fromisoformat(str(dt_obj).replace('Z', '+00:00'))
        except ValueError:
            try:
                dt_obj = datetime.strptime(str(dt_obj), '%Y-%m-%d %H:%M:%S')
            except ValueError:
                logging.warning(f"Nepodařilo se převést {dt_obj} na datetime. Vracím aktuální čas.")
                return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # Formátování pro MySQL DATETIME
    return dt_obj.strftime('%Y-%m-%d %H:%M:%S')

def format_wp_datetime_gmt(dt_obj):
    """Formátuje datetime objekt pro WordPress (GMT)."""
    # Pro zjednodušení teď použijeme stejný čas jako lokální
    return format_wp_datetime(dt_obj)

def extract_author_name(author_html):
    """Extrahuje jméno autora z HTML odkazu."""
    if not author_html:
        return None
    
    try:
        soup = BeautifulSoup(author_html, 'html.parser')
        author_link = soup.find('a')
        if author_link:
            return author_link.text.strip()
        return author_html.strip()
    except Exception as e:
        logging.error(f"Chyba při extrakci jména autora: {e}")
        return None

def copy_image_file(src_path, dest_path):
    """Kopíruje soubor obrázku z původního umístění do nového."""
    try:
        # Vytvořit cílový adresář, pokud neexistuje
        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
        
        # Kopírovat soubor
        shutil.copy2(src_path, dest_path)
        logging.info(f"Soubor zkopírován: {src_path} -> {dest_path}")
        return True
    except Exception as e:
        logging.error(f"Chyba při kopírování souboru {src_path}: {e}")
        return False

def get_mime_type(file_path):
    """Zjistí MIME typ souboru."""
    mime_type, _ = mimetypes.guess_type(file_path)
    if not mime_type and file_path.lower().endswith(('.jpg', '.jpeg')):
        return 'image/jpeg'
    elif not mime_type and file_path.lower().endswith('.png'):
        return 'image/png'
    elif not mime_type and file_path.lower().endswith('.gif'):
        return 'image/gif'
    return mime_type or 'application/octet-stream'

def update_image_urls_in_content(content, image_map):
    """
    Aktualizuje URL obrázků v obsahu článku pro databázi mujdum-2.
    
    Problém: Původní obsah obsahuje celé URL jako:
    https://www.mujdum.cz/obrazek/polozka_id/soubor.jpg
    
    Potřebujeme je nahradit za:
    http://mujdum.test/wp-content/uploads/obrazek/polozka_id/soubor.jpg
    """
    if not content or not image_map:
        return content
    
    updated_count = 0
    
    # Použijeme BeautifulSoup k identifikaci a aktualizaci obrázků v HTML
    try:
        soup = BeautifulSoup(content, 'html.parser')
        images = soup.find_all('img')
        
        for img in images:
            # Zkontrolujeme atributy src a data-src
            for attr in ['src', 'data-src']:
                if img.has_attr(attr):
                    src = img[attr]
                    original_src = src
                    
                    # Zkusíme najít mapování podle struktury URL
                    if 'mujdum.cz/obrazek/' in src or '/obrazek/' in src:
                        # Extrahujeme polozka_id/soubor.jpg z URL
                        import re
                        match = re.search(r'/obrazek/([^/]+/[^/\?]+)', src)
                        if match:
                            path_part = match.group(1)  # např. "680a1be023c59/file.jpg"
                            
                            # Najdeme odpovídající mapování
                            for old_path, new_info in image_map.items():
                                if isinstance(new_info, dict) and 'wp_url' in new_info:
                                    # Porovnáme cestu (polozka_id/soubor nebo jen soubor)
                                    if old_path == path_part or old_path.endswith('/' + os.path.basename(path_part)):
                                        img[attr] = new_info['wp_url']
                                        updated_count += 1
                                        logging.debug(f"Aktualizován obrázek podle cesty: {original_src} -> {new_info['wp_url']}")
                                        break
                                    # Zkusíme i podle názvu souboru
                                    elif os.path.basename(old_path) == os.path.basename(path_part):
                                        img[attr] = new_info['wp_url']
                                        updated_count += 1
                                        logging.debug(f"Aktualizován obrázek podle názvu souboru: {original_src} -> {new_info['wp_url']}")
                                        break
                        else:
                            # Pokud nezvládneme extrahovat cestu, zkusíme název souboru
                            src_filename = os.path.basename(src.split('?')[0])  # Odstranit query parametry
                            for old_path, new_info in image_map.items():
                                if isinstance(new_info, dict) and 'wp_url' in new_info:
                                    if os.path.basename(old_path) == src_filename:
                                        img[attr] = new_info['wp_url']
                                        updated_count += 1
                                        logging.debug(f"Aktualizován obrázek podle názvu souboru (fallback): {original_src} -> {new_info['wp_url']}")
                                        break
                    else:
                        # Pro relativní cesty nebo jiné formáty
                        src_filename = os.path.basename(src.split('?')[0])
                        for old_path, new_info in image_map.items():
                            if isinstance(new_info, dict) and 'wp_url' in new_info:
                                if os.path.basename(old_path) == src_filename:
                                    img[attr] = new_info['wp_url']
                                    updated_count += 1
                                    logging.debug(f"Aktualizován obrázek (relativní): {original_src} -> {new_info['wp_url']}")
                                    break
        
        # Konvertujeme zpět do řetězce
        content = str(soup)
        
    except Exception as e:
        logging.error(f"Chyba při aktualizaci obrázků pomocí BeautifulSoup: {e}")
        
        # Fallback na regex nahrazení
        import re
        
        # Nahradit celé URL s mujdum.cz/obrazek/
        for old_path, new_info in image_map.items():
            if isinstance(new_info, dict) and 'wp_url' in new_info:
                # Sestavit možné vzory původního URL
                old_filename = os.path.basename(old_path)
                
                # Vzor pro celé URL
                patterns = [
                    rf'https?://[^/]*mujdum\.cz/obrazek/[^"\'\s]*{re.escape(old_filename)}[^"\'\s]*',
                    rf'["\']/?obrazek/[^"\']*{re.escape(old_filename)}[^"\']*["\']',
                    rf'\b{re.escape(old_filename)}\b'
                ]
                
                for pattern in patterns:
                    if re.search(pattern, content):
                        content = re.sub(pattern, f'"{new_info["wp_url"]}"' if 'obrazek/' in pattern else new_info['wp_url'], content)
                        updated_count += 1
                        logging.debug(f"Nahrazeno regex: {pattern} -> {new_info['wp_url']}")
                        break
    
    if updated_count > 0:
        logging.info(f"Aktualizováno {updated_count} odkazů na obrázky v obsahu článku.")
    
    return content

def update_image_urls_for_galleries(gallery_shortcode, image_map):
    """
    Aktualizuje ID obrázků v gallery shortcode.
    
    Příklad: [gallery ids="123,456,789"] -> [gallery ids="1001,1002,1003"]
    kde 123,456,789 jsou původní cesty a 1001,1002,1003 jsou nová WordPress ID
    """
    if not gallery_shortcode or not image_map:
        return gallery_shortcode
    
    # Extrahovat IDs z gallery shortcode
    import re
    ids_match = re.search(r'ids="([^"]+)"', gallery_shortcode)
    if not ids_match:
        return gallery_shortcode
    
    current_ids = ids_match.group(1).split(',')
    new_ids = []
    
    for img_id in current_ids:
        img_id = img_id.strip()
        # Pokud je ID už číslo (WordPress ID), ponecháme ho
        if img_id.isdigit():
            new_ids.append(img_id)
        else:
            # Zkusíme najít odpovídající záznam v mapování
            found = False
            for old_path, new_info in image_map.items():
                if isinstance(new_info, dict) and 'wp_id' in new_info:
                    if old_path == img_id or os.path.basename(old_path) == img_id:
                        new_ids.append(str(new_info['wp_id']))
                        found = True
                        break
            
            if not found:
                logging.warning(f"Nebyl nalezen mapping pro obrázek v galerii: {img_id}")
                # Ponecháme původní ID jako fallback
                new_ids.append(img_id)
    
    # Nahradíme IDs v shortcode
    new_shortcode = gallery_shortcode.replace(ids_match.group(1), ','.join(new_ids))
    return new_shortcode
