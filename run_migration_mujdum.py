#!/usr/bin/env python3
"""
Kompletní migrační skript pro databázi MUJDUM-2
Spouští všechny potřebné migrace v správném pořadí.
"""
import logging
import sys
import os
import time
from datetime import datetime

# Importy migračních skriptů
import migrate_categories
import migrate_users  
from migrate_images_mujdum_simple import migrate_images
from migrate_articles_mujdum import migrate_articles
from migrate_galleries_mujdum import migrate_galleries_mujdum

logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_mujdum_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def run_migration_step(step_name, migration_function, *args, **kwargs):
    """Spustí jeden krok migrace s error handlingem"""
    logging.info(f"🚀 ZAČÍNÁM: {step_name}")
    start_time = time.time()
    
    try:
        result = migration_function(*args, **kwargs)
        end_time = time.time()
        duration = end_time - start_time
        logging.info(f"✅ DOKONČENO: {step_name} (trvání: {duration:.1f}s)")
        return True, result
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        logging.error(f"❌ SELHALO: {step_name} po {duration:.1f}s - Chyba: {e}")
        return False, str(e)

def main():
    """Hlavní funkce pro spuštění kompletní migrace"""
    
    print("=" * 70)
    print("🔄 MIGRACE DATABÁZE MUJDUM-2 DO WORDPRESS")
    print("=" * 70)
    print(f"Začátek: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Kontrola prostředí
    logging.info("Kontroluji prostředí...")
    
    # Zkontrolovat, zda existují potřebné soubory
    required_files = [
        'config_mujdum.py',
        'db_connectors.py', 
        'utils.py',
        '.env'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        logging.error(f"Chybí potřebné soubory: {', '.join(missing_files)}")
        sys.exit(1)
    
    # Kroky migrace
    migration_steps = [
        ("1. Migrace kategorií", migrate_categories.migrate_categories),
        ("2. Migrace uživatelů", migrate_users.migrate_users),
        ("3. Migrace obrázků", migrate_images),
        ("4. Migrace článků", migrate_articles),
        ("5. Migrace galerií", migrate_galleries_mujdum),
    ]
    
    results = {}
    failed_steps = []
    
    total_start_time = time.time()
    
    # Spustit každý krok
    for step_name, migration_func in migration_steps:
        success, result = run_migration_step(step_name, migration_func)
        results[step_name] = (success, result)
        
        if not success:
            failed_steps.append(step_name)
            
        # Krátká pauza mezi kroky
        time.sleep(2)
        print()
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # Shrnutí výsledků
    print("=" * 70)
    print("📊 SHRNUTÍ MIGRACE")
    print("=" * 70)
    
    successful_steps = len(migration_steps) - len(failed_steps)
    
    for step_name, (success, result) in results.items():
        status = "✅ ÚSPĚCH" if success else "❌ SELHALO"
        print(f"{status}: {step_name}")
        if not success:
            print(f"   └─ Chyba: {result}")
    
    print()
    print(f"📈 Celkový výsledek: {successful_steps}/{len(migration_steps)} kroků úspěšně dokončeno")
    print(f"⏱️  Celkový čas: {total_duration:.1f} sekund ({total_duration/60:.1f} minut)")
    print(f"🕐 Konec: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if failed_steps:
        print()
        print("⚠️  UPOZORNĚNÍ: Některé kroky selhaly!")
        print("   Zkontrolujte logy pro více informací.")
        print("   Můžete spustit jednotlivé kroky manuálně:")
        print()
        for step in failed_steps:
            if "kategorií" in step:
                print("   python migrate_categories.py")
            elif "uživatelů" in step:
                print("   python migrate_users.py")
            elif "obrázků" in step:
                print("   python migrate_images_mujdum.py")
            elif "článků" in step:
                print("   python migrate_articles_mujdum.py")
            elif "galerií" in step:
                print("   python migrate_galleries_mujdum.py")
        sys.exit(1)
    else:
        print()
        print("🎉 MIGRACE ÚSPĚŠNĚ DOKONČENA!")
        print()
        print("📁 Zkontrolujte následující soubory:")
        print("   - mappings/category_map.json")
        print("   - mappings/user_map.json") 
        print("   - mappings/image_map.json")
        print("   - mappings/article_map.json")
        print("   - mappings/gallery_map.json")
        print()
        print("🔧 Volitelné další kroky:")
        print("   - Spusťte opravné skripty pro optimalizaci")
        print("   - Zkontrolujte WordPress admin pro výsledky")
        
        sys.exit(0)

if __name__ == "__main__":
    main()
