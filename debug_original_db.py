#!/usr/bin/env python3
"""
Debug skript pro analýzu původní databáze - featured images a galerie
"""
import logging
from db_connectors import get_pg_connection
from config_mujdum import TBL_CLANEK, TBL_OBRAZEK

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_original_database():
    """Analyzuje původní databázi pro pochopení struktury obrázků"""
    
    print("=" * 70)
    print("🔍 ANALÝZA PŮVODNÍ DATABÁZE")
    print("=" * 70)
    
    pg_conn = get_pg_connection()
    pg_cursor = pg_conn.cursor()
    
    # 1. Analýza featured images (obrazek_src)
    print("\n1. FEATURED IMAGES (obrazek_src):")
    print("-" * 40)
    
    pg_cursor.execute(f"""
        SELECT COUNT(*) as total_articles,
               COUNT(obrazek_src) as articles_with_image,
               COUNT(CASE WHEN obrazek_src IS NOT NULL AND obrazek_src != '' THEN 1 END) as articles_with_nonempty_image
        FROM {TBL_CLANEK}
    """)
    stats = pg_cursor.fetchone()
    print(f"📊 Celkem článků: {stats[0]}")
    print(f"📊 S obrazek_src: {stats[1]}")
    print(f"📊 S neprázdným obrazek_src: {stats[2]}")
    
    # Vzorové featured images
    pg_cursor.execute(f"""
        SELECT id_clanek, nazev, obrazek_src, unikatni_id
        FROM {TBL_CLANEK} 
        WHERE obrazek_src IS NOT NULL AND obrazek_src != ''
        LIMIT 10
    """)
    
    featured_samples = pg_cursor.fetchall()
    print(f"\n🖼️ Vzorové featured images:")
    for article_id, title, img_src, unique_id in featured_samples:
        print(f"   Článek {article_id}: {title[:40]}...")
        print(f"      obrazek_src: {img_src}")
        print(f"      unikatni_id: {unique_id}")
        print()
    
    # 2. Analýza tabulky obrazek
    print("\n2. TABULKA OBRAZEK:")
    print("-" * 40)
    
    pg_cursor.execute(f"""
        SELECT COUNT(*) as total_images,
               COUNT(DISTINCT polozka_id) as unique_polozka_ids,
               MIN(priorita) as min_priorita,
               MAX(priorita) as max_priorita
        FROM {TBL_OBRAZEK}
        WHERE soubor IS NOT NULL AND soubor != ''
    """)
    stats = pg_cursor.fetchone()
    print(f"📊 Celkem obrázků: {stats[0]}")
    print(f"📊 Unikátní polozka_id: {stats[1]}")
    print(f"📊 Priorita rozsah: {stats[2]} - {stats[3]}")
    
    # Analýza podle typu
    pg_cursor.execute(f"""
        SELECT typ, COUNT(*) as count
        FROM {TBL_OBRAZEK}
        WHERE soubor IS NOT NULL AND soubor != ''
        GROUP BY typ
        ORDER BY typ
    """)
    type_stats = pg_cursor.fetchall()
    print(f"\n📈 Rozdělení podle typu:")
    for typ, count in type_stats:
        print(f"   Typ {typ}: {count} obrázků")
    
    # 3. Propojení článků s obrázky
    print("\n3. PROPOJENÍ ČLÁNKŮ S OBRÁZKY:")
    print("-" * 40)
    
    pg_cursor.execute(f"""
        SELECT c.id_clanek, c.nazev, c.unikatni_id, c.obrazek_src,
               COUNT(o.id_obrazek) as image_count,
               STRING_AGG(DISTINCT o.typ::text, ',') as image_types,
               STRING_AGG(o.soubor, '|||' ORDER BY o.priorita DESC) as image_files
        FROM {TBL_CLANEK} c
        LEFT JOIN {TBL_OBRAZEK} o ON c.unikatni_id = o.polozka_id
        WHERE c.id_clanek IN (
            SELECT id_clanek FROM {TBL_CLANEK} 
            WHERE obrazek_src IS NOT NULL AND obrazek_src != ''
            LIMIT 5
        )
        GROUP BY c.id_clanek, c.nazev, c.unikatni_id, c.obrazek_src
        ORDER BY image_count DESC
    """)
    
    connections = pg_cursor.fetchall()
    print(f"🔗 Propojení článek -> obrázky:")
    for article_id, title, unique_id, featured_src, img_count, types, files in connections:
        print(f"\n   Článek {article_id}: {title[:40]}...")
        print(f"      unikatni_id: {unique_id}")
        print(f"      obrazek_src: {featured_src}")
        print(f"      Počet obrázků v galerii: {img_count}")
        print(f"      Typy obrázků: {types}")
        if files:
            file_list = files.split('|||')[:3]  # jen první 3
            for i, file in enumerate(file_list, 1):
                print(f"         {i}. {file}")
            if img_count > 3:
                print(f"         ... a dalších {img_count - 3}")
    
    # 4. Analýza problematických případů
    print("\n4. PROBLEMATICKÉ PŘÍPADY:")
    print("-" * 40)
    
    # Články s featured image ale bez galerie
    pg_cursor.execute(f"""
        SELECT COUNT(*)
        FROM {TBL_CLANEK} c
        WHERE c.obrazek_src IS NOT NULL AND c.obrazek_src != ''
        AND NOT EXISTS (
            SELECT 1 FROM {TBL_OBRAZEK} o 
            WHERE o.polozka_id = c.unikatni_id
        )
    """)
    no_gallery = pg_cursor.fetchone()[0]
    print(f"📊 Články s featured image ale bez galerie: {no_gallery}")
    
    # Články bez featured image ale s galerií
    pg_cursor.execute(f"""
        SELECT COUNT(DISTINCT c.id_clanek)
        FROM {TBL_CLANEK} c
        JOIN {TBL_OBRAZEK} o ON c.unikatni_id = o.polozka_id
        WHERE (c.obrazek_src IS NULL OR c.obrazek_src = '')
    """)
    no_featured = pg_cursor.fetchone()[0]
    print(f"📊 Články bez featured image ale s galerií: {no_featured}")
    
    # 5. Doporučení pro mapování
    print("\n5. DOPORUČENÍ PRO MAPOVÁNÍ:")
    print("-" * 40)
    print("🔧 Pro featured images:")
    print("   - Použít obrazek_src z tabulky clanek")
    print("   - Mapovat podle přesného názvu souboru")
    print()
    print("🔧 Pro galerie:")
    print("   - Použít tabulku obrazek s polozka_id = unikatni_id")
    print("   - Seřadit podle priorita DESC")
    print("   - Rozlišit podle typu (0=featured?, 1=galerie?)")
    print()
    print("🔧 Pro duplikáty:")
    print("   - Zkontrolovat, zda featured image není také v galerii")
    print("   - Možná vyloučit první obrázek z galerie pokud je stejný jako featured")
    
    pg_cursor.close()
    pg_conn.close()

if __name__ == "__main__":
    debug_original_database()
