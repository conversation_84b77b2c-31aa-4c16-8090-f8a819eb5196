#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> lepší verze obrázků napříč celou databází
Najde největší dostupné verze podobných obrázků z jakéhokoliv článku
"""
import json
import re
import os
import logging
from collections import defaultdict
from db_connectors import get_mysql_connection
from config_mujdum import TBL_WP_POSTS, TBL_WP_POSTMETA
from utils_mujdum import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class GlobalImageMatcher:
    def __init__(self, image_map):
        self.image_map = image_map
        self.global_image_index = defaultdict(list)
        self._build_global_index()
    
    def _build_global_index(self):
        """Postaví globální index všech obrázků podle základních názvů"""
        logging.info("Buduje globální index obr<PERSON>zků...")
        
        for path, info in self.image_map.items():
            if not isinstance(info, dict) or 'wp_url' not in info:
                continue
                
            filename = os.path.basename(info['wp_url'])
            base_name = self.get_base_filename(filename)
            
            # Extrahovat rozměry
            width, height = self.extract_dimensions(filename)
            size = (width * height) if width and height else 999999
            
            # Extrahovat článek
            article_id = self.extract_article_id(path)
            
            self.global_image_index[base_name].append({
                'wp_id': info['wp_id'],
                'wp_url': info['wp_url'],
                'path': path,
                'filename': filename,
                'width': width or 9999,
                'height': height or 9999,
                'size': size,
                'article_id': article_id,
                'is_original': not bool(width and height)
            })
        
        # Seřadit každou skupinu podle velikosti
        for base_name in self.global_image_index:
            self.global_image_index[base_name].sort(key=lambda x: x['size'], reverse=True)
        
        logging.info(f"Index postaven pro {len(self.global_image_index)} základních názvů")
    
    def extract_dimensions(self, filename):
        """Extrahuje rozměry z názvu souboru"""
        match = re.search(r'_(\d+)x(\d+)', filename)
        if match:
            return int(match.group(1)), int(match.group(2))
        return None, None
    
    def get_base_filename(self, filename):
        """Získá základní název souboru"""
        base = re.sub(r'_\d+x\d+', '', filename)
        base = re.sub(r'-[a-f0-9]{10,}', '', base)
        return base
    
    def extract_article_id(self, path):
        """Extrahuje ID článku z cesty"""
        match = re.search(r'([a-f0-9]{10,})', path)
        return match.group(1) if match else None
    
    def find_best_global_version(self, current_filename):
        """Najde nejlepší globální verzi obrázku"""
        base_name = self.get_base_filename(current_filename)
        
        if base_name in self.global_image_index:
            versions = self.global_image_index[base_name]
            
            if versions:
                # Vrátit největší verzi
                best = versions[0]
                return {
                    'wp_id': best['wp_id'],
                    'wp_url': best['wp_url'],
                    'filename': best['filename'],
                    'dimensions': f"{best['width']}x{best['height']}" if best['width'] != 9999 else "ORIGINAL",
                    'size': best['size'],
                    'article_id': best['article_id'],
                    'is_original': best['is_original']
                }
        
        return None

def fix_article_with_global_search(article_unique_id, mysql_cursor, matcher):
    """Opraví článek pomocí globálního hledání lepších verzí"""
    
    print(f"\n🔧 GLOBÁLNÍ OPRAVA ČLÁNKU: {article_unique_id}")
    print("=" * 60)
    
    # Najít WordPress post
    mysql_cursor.execute(f"""
        SELECT p.ID, p.post_title, p.post_content, pm.meta_value as thumbnail_id
        FROM {TBL_WP_POSTS} p
        JOIN {TBL_WP_POSTMETA} pm_version ON p.ID = pm_version.post_id
        LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
        WHERE p.post_type = 'post' 
        AND pm_version.meta_key = 'sabre_database_version' 
        AND pm_version.meta_value = 'mujdum-2'
        AND p.post_content LIKE %s
    """, (f"%{article_unique_id}%",))
    
    post_result = mysql_cursor.fetchone()
    
    if not post_result:
        print("❌ WordPress post nenalezen")
        return False
        
    post_id, post_title, post_content, current_thumbnail = post_result
    print(f"📖 Post: {post_id} - {post_title}")
    
    # Najít všechny image ID
    image_ids = set()
    
    # Z galerií
    gallery_matches = re.findall(r'\[gallery[^\]]*ids="([^"]+)"[^\]]*\]', post_content)
    for match in gallery_matches:
        ids = [id.strip() for id in match.split(',') if id.strip().isdigit()]
        image_ids.update(map(int, ids))
    
    # Featured image
    if current_thumbnail and current_thumbnail.isdigit():
        image_ids.add(int(current_thumbnail))
    
    print(f"📊 Analyzuji {len(image_ids)} obrázků:")
    
    replacements = []
    
    for img_id in image_ids:
        # Získat současný obrázek
        mysql_cursor.execute(f"""
            SELECT guid FROM {TBL_WP_POSTS} WHERE ID = %s AND post_type = 'attachment'
        """, (img_id,))
        
        result = mysql_cursor.fetchone()
        if not result:
            continue
            
        current_url = result[0]
        current_filename = os.path.basename(current_url)
        current_size = matcher.extract_dimensions(current_filename)
        
        # Najít nejlepší globální verzi
        best_global = matcher.find_best_global_version(current_filename)
        
        if best_global and best_global['wp_id'] != img_id:
            print(f"\n🔍 Obrázek ID {img_id}:")
            print(f"   📏 Současný: {current_size[0] if current_size[0] else '?'}x{current_size[1] if current_size[1] else '?'}")
            print(f"   🎯 Nejlepší: {best_global['dimensions']} (ID: {best_global['wp_id']})")
            print(f"   📂 Ze článku: {best_global['article_id']}")
            
            # Kontrola zlepšení
            if best_global['is_original'] or (current_size[0] and best_global['size'] > current_size[0] * current_size[1]):
                print(f"   ✅ NAHRADÍM!")
                
                replacements.append({
                    'old_id': img_id,
                    'new_id': best_global['wp_id'],
                    'old_filename': current_filename,
                    'new_filename': best_global['filename'],
                    'improvement': f"{'x'.join(map(str, current_size)) if current_size[0] else '?'} -> {best_global['dimensions']}"
                })
            else:
                print(f"   ⚠️  Není výrazné zlepšení")
        else:
            print(f"\n⚠️  Obrázek ID {img_id}: Už je nejlepší dostupná verze")
    
    if not replacements:
        print("\n✅ Všechny obrázky už používají nejlepší dostupné verze")
        return True
    
    print(f"\n🎯 APLIKUJI {len(replacements)} GLOBÁLNÍCH NÁHRAD:")
    
    # Aplikovat náhrady
    updated_content = post_content
    featured_updated = False
    
    for replacement in replacements:
        old_id = replacement['old_id']
        new_id = replacement['new_id']
        
        print(f"   📝 {old_id} -> {new_id} ({replacement['improvement']})")
        
        # Nahradit v obsahu
        updated_content = re.sub(rf'\b{old_id}\b', str(new_id), updated_content)
        
        # Nahradit featured image
        if current_thumbnail and int(current_thumbnail) == old_id:
            mysql_cursor.execute(f"""
                UPDATE {TBL_WP_POSTMETA} 
                SET meta_value = %s 
                WHERE post_id = %s AND meta_key = '_thumbnail_id'
            """, (new_id, post_id))
            featured_updated = True
    
    # Uložit aktualizovaný obsah
    mysql_cursor.execute(f"""
        UPDATE {TBL_WP_POSTS} 
        SET post_content = %s 
        WHERE ID = %s
    """, (updated_content, post_id))
    
    print(f"\n✅ GLOBÁLNÍ OPRAVA DOKONČENA:")
    print(f"   📝 Obsah aktualizován: {len(replacements)} náhrad")
    if featured_updated:
        print(f"   🎯 Featured image aktualizován")
    
    return True

def main():
    """Hlavní funkce"""
    
    article_unique_id = "665510acb770a"
    article_title = "Přeměňte svou zahradu na stylový relaxační koutek"
    
    print("=" * 80)
    print(f"🌍 GLOBÁLNÍ HLEDÁNÍ LEPŠÍCH VERZÍ OBRÁZKŮ")
    print("=" * 80)
    print(f"📖 Článek: {article_title}")
    print(f"🆔 Unique ID: {article_unique_id}")
    print()
    
    # Načíst mapování
    logging.info("Načítám mapování obrázků...")
    image_map = load_mapping('image_map.json')
    logging.info(f"Načteno {len(image_map)} mapování obrázků")
    
    # Připojit k databázi
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Inicializovat matcher
        matcher = GlobalImageMatcher(image_map)
        
        # Opravit článek
        success = fix_article_with_global_search(article_unique_id, mysql_cursor, matcher)
        
        if success:
            mysql_conn.commit()
            print(f"\n🎉 GLOBÁLNÍ OPRAVA ÚSPĚŠNĚ DOKONČENA!")
            print(f"🔍 Spusť debug: python debug_specific_article.py \"{article_title}\"")
        else:
            print(f"\n❌ GLOBÁLNÍ OPRAVA SE NEZDAŘILA")
            
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Chyba: {e}")
        print(f"\n💥 CHYBA: {e}")
    finally:
        mysql_cursor.close()
        mysql_conn.close()

if __name__ == "__main__":
    main()
